{"PerformanceOptimization": {"DataCache": {"DefaultCacheExpiry": "00:05:00", "HistoryCacheExpiry": "00:30:00", "MaxCacheSize": 1000, "CleanupInterval": "00:05:00"}, "AdaptiveRefresh": {"MinRefreshInterval": "00:00:00.500", "MaxRefreshInterval": "00:00:10", "DefaultRefreshInterval": "00:00:02", "ChangeHistorySize": 10, "HighChangeThreshold": 0.7, "LowChangeThreshold": 0.2}, "ConnectionRetry": {"MaxRetryAttempts": 5, "BaseRetryDelay": "00:00:02", "MaxRetryDelay": "00:05:00", "HealthCheckInterval": "00:00:30"}, "PerformanceMonitor": {"MonitorInterval": "00:00:30", "HistorySize": 100, "CpuWarningThreshold": 80.0, "MemoryWarningThresholdMB": 500, "ResponseTimeWarningThresholdMs": 1000}, "UIOptimization": {"EnableSmartUIUpdates": true, "BatchUIUpdates": true, "UIUpdateThrottleMs": 100, "MaxUIUpdateQueueSize": 50}}, "Logging": {"PerformanceLogging": {"EnablePerformanceLogs": true, "LogLevel": "Information", "LogCacheHits": false, "LogRefreshIntervalChanges": true, "LogRetryAttempts": true, "LogPerformanceAlerts": true}}}