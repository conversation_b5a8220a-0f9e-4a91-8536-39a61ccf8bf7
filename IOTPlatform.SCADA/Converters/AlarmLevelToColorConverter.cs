using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using IOTPlatform.SCADA.Models;

namespace IOTPlatform.SCADA.Converters;

/// <summary>
/// 报警级别到颜色转换器
/// </summary>
public class AlarmLevelToColorConverter : IValueConverter
{
    public static readonly AlarmLevelToColorConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is AlarmLevel level)
        {
            return level switch
            {
                AlarmLevel.Critical => new SolidColorBrush(Color.FromRgb(231, 76, 60)),   // 红色
                AlarmLevel.Critical => new SolidColorBrush(Color.FromRgb(230, 126, 34)),      // 橙色
                AlarmLevel.Warning => new SolidColorBrush(Color.FromRgb(241, 196, 15)),    // 黄色
                AlarmLevel.Info => new SolidColorBrush(Color.FromRgb(39, 174, 96)),        // 绿色
                _ => new SolidColorBrush(Colors.Transparent)
            };
        }
        
        return new SolidColorBrush(Colors.Transparent);
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 报警状态到颜色转换器
/// </summary>
public class AlarmStatusToColorConverter : IValueConverter
{
    public static readonly AlarmStatusToColorConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is AlarmStatus status)
        {
            return status switch
            {
                AlarmStatus.Active => new SolidColorBrush(Color.FromRgb(231, 76, 60)),      // 红色
                AlarmStatus.Acknowledged => new SolidColorBrush(Color.FromRgb(241, 196, 15)), // 黄色
                AlarmStatus.Recovered => new SolidColorBrush(Color.FromRgb(39, 174, 96)),    // 绿色
                AlarmStatus.Suppressed => new SolidColorBrush(Color.FromRgb(149, 165, 166)), // 灰色
                _ => new SolidColorBrush(Colors.Transparent)
            };
        }
        
        return new SolidColorBrush(Colors.Transparent);
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 布尔值到可见性转换器
/// </summary>
public class BooleanToVisibilityConverter : IValueConverter
{
    public static readonly BooleanToVisibilityConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is Avalonia.Controls.Visibility Avalonia.Controls.VisibilityValue)
        {
            return Avalonia.Controls.VisibilityValue ? Avalonia.Controls.Visibility.Visible : Avalonia.Controls.Visibility.Collapsed;
        }
        
        return Avalonia.Controls.Visibility.Collapsed;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is Avalonia.Controls.Visibility visibility)
        {
            return visibility == Avalonia.Controls.Visibility.Visible;
        }
        
        return Avalonia.Controls.Visibility.Collapsed;
    }
}

/// <summary>
/// 对象非空到可见性转换器
/// </summary>
public class ObjectNotNullToVisibilityConverter : IValueConverter
{
    public static readonly ObjectNotNullToVisibilityConverter Instance = new();

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        return value != null ? Avalonia.Controls.Visibility.Visible : Avalonia.Controls.Visibility.Collapsed;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
