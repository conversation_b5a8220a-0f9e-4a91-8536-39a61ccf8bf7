# SCADA系统性能优化指南

## 概述

本文档介绍了IOTPlatform.SCADA系统的性能优化功能，包括数据缓存、自适应刷新、连接重试和性能监控等特性。

## 性能优化组件

### 1. 数据缓存服务 (DataCacheService)

#### 功能特性
- **内存缓存**：缓存设备数据和历史查询结果
- **自动过期**：支持可配置的缓存过期时间
- **容量管理**：自动清理最旧的缓存项
- **数据去重**：检测数据变化，避免重复处理

#### 使用方法
```csharp
// 获取缓存数据
var cachedValue = _cacheService.GetCachedData<double?>("device_data:temperature");

// 设置缓存数据
_cacheService.SetCachedData("device_data:temperature", 25.5, TimeSpan.FromMinutes(5));

// 检查数据是否变化
var hasChanged = _cacheService.HasDataChanged("last_value:temperature", newValue);
```

#### 配置参数
- `DefaultCacheExpiry`: 默认缓存过期时间 (5分钟)
- `HistoryCacheExpiry`: 历史数据缓存过期时间 (30分钟)
- `MaxCacheSize`: 最大缓存项数量 (1000)

### 2. 自适应刷新服务 (AdaptiveRefreshService)

#### 功能特性
- **动态调整**：根据数据变化频率自动调整刷新间隔
- **智能算法**：高频变化时加快刷新，低频变化时减慢刷新
- **性能优化**：减少不必要的网络请求和CPU使用

#### 工作原理
1. 监控最近10次数据变化情况
2. 计算变化率：变化次数 / 总次数
3. 根据阈值调整刷新间隔：
   - 变化率 ≥ 70%：减少刷新间隔 (加快)
   - 变化率 ≤ 20%：增加刷新间隔 (减慢)
   - 其他情况：保持当前间隔

#### 配置参数
- `MinRefreshInterval`: 最小刷新间隔 (500ms)
- `MaxRefreshInterval`: 最大刷新间隔 (10秒)
- `DefaultRefreshInterval`: 默认刷新间隔 (2秒)

### 3. 连接重试服务 (ConnectionRetryService)

#### 功能特性
- **指数退避**：重试延迟逐渐增加
- **健康检查**：定期检查连接状态
- **事件通知**：提供重试过程的详细事件

#### 重试策略
- 最大重试次数：5次
- 基础延迟：2秒
- 延迟算法：delay = baseDelay × 2^(attemptNumber-1)
- 最大延迟：5分钟

#### 使用示例
```csharp
var success = await _retryService.ExecuteWithRetryAsync(async () =>
{
    return await _gatewayService.StartAsync();
});
```

### 4. 性能监控服务 (PerformanceMonitorService)

#### 监控指标
- **CPU使用率**：进程CPU占用百分比
- **内存使用**：工作集内存大小
- **线程数量**：当前线程数
- **响应时间**：数据处理响应时间
- **错误计数**：错误发生次数

#### 警告阈值
- CPU使用率 > 80%
- 内存使用 > 500MB
- 响应时间 > 1000ms

## 优化版ViewModel

### SCADAMonitorViewModelOptimized

相比原版ViewModel，优化版本提供以下改进：

#### 1. 智能UI更新
```csharp
private bool SetPropertyIfChanged<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
{
    if (EqualityComparer<T>.Default.Equals(field, value))
    {
        return false; // 值没有变化，不触发通知
    }
    
    field = value;
    RaisePropertyChanged(propertyName);
    return true;
}
```

#### 2. 并行数据刷新
```csharp
private async Task RefreshDataWithCacheAsync()
{
    var refreshTasks = new List<Task>();
    
    foreach (var device in Devices)
    {
        refreshTasks.Add(RefreshDeviceDataAsync(device));
    }
    
    await Task.WhenAll(refreshTasks);
}
```

#### 3. 缓存优先策略
- 优先使用缓存数据
- 缓存未命中时才访问网络
- 自动缓存新获取的数据

## 使用指南

### 1. 启用性能优化

在SCADAModule中，性能优化服务已自动注册：

```csharp
// 注册性能优化服务
containerRegistry.RegisterSingleton<DataCacheService>();
containerRegistry.RegisterSingleton<ConnectionRetryService>();
containerRegistry.RegisterSingleton<AdaptiveRefreshService>();
containerRegistry.RegisterSingleton<PerformanceMonitorService>();
```

### 2. 使用优化版ViewModel

要使用性能优化版本的ViewModel，需要在导航时指定：

```csharp
// 使用优化版ViewModel
containerRegistry.RegisterForNavigation<SCADAMonitorView, SCADAMonitorViewModelOptimized>();
```

### 3. 配置优化参数

编辑 `Config/PerformanceConfig.json` 文件来调整性能参数：

```json
{
  "PerformanceOptimization": {
    "DataCache": {
      "DefaultCacheExpiry": "00:05:00",
      "MaxCacheSize": 1000
    },
    "AdaptiveRefresh": {
      "MinRefreshInterval": "00:00:00.500",
      "MaxRefreshInterval": "00:00:10"
    }
  }
}
```

### 4. 监控性能

通过性能监控服务获取实时性能数据：

```csharp
var summary = _performanceMonitorService.GetPerformanceSummary();
Console.WriteLine($"平均CPU使用率: {summary.AverageCpuUsage:F1}%");
Console.WriteLine($"平均内存使用: {summary.AverageMemoryUsage / (1024*1024):F0}MB");
```

## 性能提升效果

### 预期改进

1. **响应速度提升 30-50%**
   - 数据缓存减少网络请求
   - 智能UI更新减少界面刷新

2. **CPU使用率降低 20-40%**
   - 自适应刷新减少不必要的处理
   - 并行处理提高效率

3. **内存使用优化 15-25%**
   - 缓存容量管理
   - 及时释放资源

4. **网络流量减少 40-60%**
   - 缓存命中减少重复请求
   - 智能刷新间隔调整

### 性能测试建议

1. **基准测试**：记录优化前的性能指标
2. **压力测试**：模拟高频数据变化场景
3. **长期测试**：运行24小时以上观察内存泄漏
4. **对比测试**：原版vs优化版性能对比

## 故障排除

### 常见问题

1. **缓存命中率低**
   - 检查缓存过期时间设置
   - 确认数据变化频率

2. **刷新间隔不合理**
   - 调整变化率阈值
   - 检查数据变化检测逻辑

3. **连接重试失败**
   - 检查网络连接
   - 调整重试参数

### 调试技巧

1. **启用详细日志**
```json
{
  "Logging": {
    "PerformanceLogging": {
      "LogLevel": "Debug",
      "LogCacheHits": true
    }
  }
}
```

2. **监控性能指标**
```csharp
_performanceMonitorService.PerformanceAlert += (sender, args) =>
{
    Console.WriteLine($"性能警告: {string.Join(", ", args.Alerts)}");
};
```

## 总结

通过实施这些性能优化措施，SCADA系统能够：

- 🚀 **提升响应速度**：更快的数据更新和界面响应
- 💾 **优化资源使用**：更低的CPU和内存占用
- 🔄 **智能自适应**：根据实际情况动态调整性能参数
- 📊 **实时监控**：全面的性能指标和警告机制
- 🛡️ **提高稳定性**：更好的错误处理和重试机制

这些优化使SCADA系统能够在各种工业环境中稳定高效地运行。
