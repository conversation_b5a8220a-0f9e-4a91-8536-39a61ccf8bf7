using IOTPlatform.InfluxDbWrapper.Services;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using Serilog;

namespace IOTPlatform.SCADA.Examples;

/// <summary>
/// IoT网关使用示例
/// </summary>
public class IoTGatewayExample
{
    private readonly ILogger _logger;
    private IoTGatewayService _gatewayService;

    public IoTGatewayExample()
    {
        // 配置日志
        _logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File("logs/iot-gateway-example-.log", rollingInterval: RollingInterval.Day)
            .CreateLogger();
    }

    /// <summary>
    /// 运行完整示例
    /// </summary>
    public async Task RunExampleAsync()
    {
        try
        {
            _logger.Information("开始IoT网关示例演示");

            // 1. 初始化服务
            await InitializeServicesAsync();

            // 2. 配置设备
            ConfigureDevices();

            // 3. 启动网关服务
            await StartGatewayServiceAsync();

            // 4. 演示数据采集
            await DemonstrateDataCollectionAsync();

            // 5. 演示设备控制
            await DemonstrateDeviceControlAsync();

            // 6. 演示数据查询
            await DemonstrateDataQueryAsync();

            // 7. 运行一段时间
            _logger.Information("网关服务运行中，按任意键停止...");
            Console.ReadKey();

            // 8. 停止服务
            await StopGatewayServiceAsync();

            _logger.Information("IoT网关示例演示完成");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "IoT网关示例运行异常");
        }
    }

    /// <summary>
    /// 初始化服务
    /// </summary>
    private async Task InitializeServicesAsync()
    {
        _logger.Information("初始化IoT网关服务...");

        // 创建配置服务
        var configService = new ConfigurationService(_logger);

        // 创建InfluxDB服务
        var influxConfig = configService.GetInfluxDbConfiguration();
        var influxDbService = new InfluxDbService(
            new IOTPlatform.InfluxDbWrapper.Models.InfluxDbConfiguration
            {
                Url = influxConfig.Url,
                Token = influxConfig.Token,
                Organization = influxConfig.Organization,
                Bucket = influxConfig.Bucket,
                Timeout = TimeSpan.FromSeconds(influxConfig.TimeoutSeconds)
            }, 
            _logger);

        // 创建通信服务
        var communicationService = new MrpcCommunicationService(_logger);

        // 创建各个子服务
        var dataService = new EdgeGatewayDataService(communicationService, _logger);
        var timeSeriesService = new TimeSeriesDataService(influxDbService, _logger);
        var controlService = new EdgeGatewayControlService(communicationService, _logger);

        // 创建主服务
        _gatewayService = new IoTGatewayService(
            dataService, 
            timeSeriesService, 
            controlService, 
            configService, 
            _logger);

        // 注册事件处理
        _gatewayService.DataPointReceived += OnDataPointReceived;
        _gatewayService.AlarmReceived += OnAlarmReceived;
        _gatewayService.ErrorOccurred += OnErrorOccurred;
        _gatewayService.StatusChanged += OnStatusChanged;

        _logger.Information("IoT网关服务初始化完成");
    }

    /// <summary>
    /// 配置设备
    /// </summary>
    private void ConfigureDevices()
    {
        _logger.Information("配置设备路径...");

        // 添加示例设备路径（根据实际边缘网关配置调整）
        var devicePaths = new[]
        {
            "工厂一/车间A/设备A01",
            "工厂一/车间A/设备A02", 
            "工厂一/车间B/设备B01",
            "工厂二/生产线1/设备001",
            "工厂二/生产线1/设备002"
        };

        foreach (var devicePath in devicePaths)
        {
            _gatewayService.AddDevicePath(devicePath);
            _logger.Information("已添加设备路径: {DevicePath}", devicePath);
        }
    }

    /// <summary>
    /// 启动网关服务
    /// </summary>
    private async Task StartGatewayServiceAsync()
    {
        _logger.Information("启动IoT网关服务...");

        var success = await _gatewayService.StartAsync();
        if (success)
        {
            _logger.Information("IoT网关服务启动成功");
        }
        else
        {
            throw new Exception("IoT网关服务启动失败");
        }
    }

    /// <summary>
    /// 演示数据采集
    /// </summary>
    private async Task DemonstrateDataCollectionAsync()
    {
        _logger.Information("演示数据采集功能...");

        // 等待一段时间让数据采集运行
        await Task.Delay(10000);

        // 显示统计信息
        var stats = _gatewayService.GetStatistics();
        _logger.Information("数据采集统计: 已采集 {DataPoints} 个数据点, {Alarms} 个报警", 
            stats.TotalDataPointsCollected, stats.TotalAlarmsReceived);
    }

    /// <summary>
    /// 演示设备控制
    /// </summary>
    private async Task DemonstrateDeviceControlAsync()
    {
        _logger.Information("演示设备控制功能...");

        var devicePath = "工厂一/车间A/设备A01";

        try
        {
            // 1. 获取设备状态
            var status = await _gatewayService.GetDeviceStatusAsync(devicePath);
            _logger.Information("设备状态: {DevicePath} - {Status}", devicePath, status.Status);

            // 2. 写入设备数据
            var writeSuccess = await _gatewayService.WriteDeviceDataAsync($"{devicePath}/产量", 100);
            _logger.Information("写入设备数据结果: {Success}", writeSuccess ? "成功" : "失败");

            // 3. 调用设备方法（如果设备支持）
            try
            {
                var methodResult = await _gatewayService.CallDeviceMethodAsync(devicePath, "GetInfo");
                _logger.Information("调用设备方法结果: {Result}", methodResult);
            }
            catch (Exception ex)
            {
                _logger.Warning("调用设备方法失败: {Error}", ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "设备控制演示异常");
        }
    }

    /// <summary>
    /// 演示数据查询
    /// </summary>
    private async Task DemonstrateDataQueryAsync()
    {
        _logger.Information("演示数据查询功能...");

        try
        {
            var deviceId = "工厂一_车间A_设备A01";
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-1);

            // 查询历史数据
            var historyData = await _gatewayService.QueryHistoryDataAsync(deviceId, startTime, endTime);
            _logger.Information("查询到历史数据: {Count} 条", historyData.Count());

            // 查询历史报警
            var historyAlarms = await _gatewayService.QueryHistoryAlarmsAsync(deviceId, startTime, endTime);
            _logger.Information("查询到历史报警: {Count} 条", historyAlarms.Count());
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "数据查询演示异常");
        }
    }

    /// <summary>
    /// 停止网关服务
    /// </summary>
    private async Task StopGatewayServiceAsync()
    {
        _logger.Information("停止IoT网关服务...");

        await _gatewayService.StopAsync();
        _gatewayService.Dispose();

        _logger.Information("IoT网关服务已停止");
    }

    #region 事件处理

    private void OnDataPointReceived(IoTDataPoint dataPoint)
    {
        _logger.Debug("收到数据点: {DeviceId} = {Value} {Unit}", 
            dataPoint.DeviceId, dataPoint.Value, dataPoint.Unit);
    }

    private void OnAlarmReceived(object sender, AlarmInfo alarm)
    {
        _logger.Warning("收到报警: {DeviceId} - {Level} - {Message}", 
            alarm.DeviceId, alarm.Level, alarm.Message);
    }

    private void OnErrorOccurred(Exception ex)
    {
        _logger.Error(ex, "网关服务异常");
    }

    private void OnStatusChanged(object sender, string status)
    {
        _logger.Information("网关状态变更: {Status}", status);
    }

    #endregion
}

/// <summary>
/// 程序入口点示例
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var example = new IoTGatewayExample();
        await example.RunExampleAsync();
    }
}
