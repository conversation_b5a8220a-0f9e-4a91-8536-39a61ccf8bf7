using System.ComponentModel;

namespace IOTPlatform.SCADA.Models;

/// <summary>
/// 报警规则
/// </summary>
public class AlarmRule
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string DevicePath { get; set; } = "";
    public string DeviceName { get; set; } = "";
    public AlarmType AlarmType { get; set; }
    public AlarmLevel Level { get; set; }
    public AlarmCondition Condition { get; set; }
    public double Threshold { get; set; }
    public double? HighThreshold { get; set; }
    public double? LowThreshold { get; set; }
    public TimeSpan DelayTime { get; set; } = TimeSpan.Zero;
    public bool IsEnabled { get; set; } = true;
    public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
    public DateTime? LastTriggeredTime { get; set; }
    public string CreatedBy { get; set; } = "";
    public string Category { get; set; } = "";
    public Dictionary<string, object> Properties { get; set; } = new();
    
    /// <summary>
    /// 报警消息模板
    /// </summary>
    public string MessageTemplate { get; set; } = "{DeviceName} {Condition} {Threshold}";
    
    /// <summary>
    /// 报警恢复消息模板
    /// </summary>
    public string RecoveryMessageTemplate { get; set; } = "{DeviceName} 已恢复正常";
    
    /// <summary>
    /// 是否需要确认
    /// </summary>
    public bool RequireAcknowledgment { get; set; } = true;
    
    /// <summary>
    /// 自动恢复
    /// </summary>
    public bool AutoRecovery { get; set; } = true;
    
    /// <summary>
    /// 通知方式
    /// </summary>
    public NotificationMethod NotificationMethods { get; set; } = NotificationMethod.UI;
}

/// <summary>
/// 报警类型
/// </summary>
public enum AlarmType
{
    [Description("数值报警")]
    Value,
    [Description("状态报警")]
    Status,
    [Description("通信报警")]
    Communication,
    [Description("系统报警")]
    System
}

/// <summary>
/// 报警条件
/// </summary>
public enum AlarmCondition
{
    [Description("大于")]
    GreaterThan,
    [Description("小于")]
    LessThan,
    [Description("等于")]
    Equal,
    [Description("不等于")]
    NotEqual,
    [Description("范围内")]
    InRange,
    [Description("范围外")]
    OutOfRange,
    [Description("变化率过大")]
    ChangeRateHigh,
    [Description("设备离线")]
    DeviceOffline,
    [Description("通信超时")]
    CommunicationTimeout
}

/// <summary>
/// 通知方式
/// </summary>
[Flags]
public enum NotificationMethod
{
    None = 0,
    UI = 1,
    Email = 2,
    SMS = 4,
    Sound = 8,
    Log = 16,
    All = UI | Email | SMS | Sound | Log
}

/// <summary>
/// 报警实例
/// </summary>
public class AlarmInstance
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string RuleId { get; set; } = "";
    public string RuleName { get; set; } = "";
    public string DevicePath { get; set; } = "";
    public string DeviceName { get; set; } = "";
    public AlarmLevel Level { get; set; }
    public AlarmStatus Status { get; set; } = AlarmStatus.Active;
    public string Message { get; set; } = "";
    public double CurrentValue { get; set; }
    public double Threshold { get; set; }
    public DateTime TriggeredTime { get; set; } = DateTime.UtcNow;
    public DateTime? AcknowledgedTime { get; set; }
    public DateTime? RecoveredTime { get; set; }
    public string AcknowledgedBy { get; set; } = "";
    public string AcknowledgmentComment { get; set; } = "";
    public int Count { get; set; } = 1;
    public TimeSpan Duration => RecoveredTime?.Subtract(TriggeredTime) ?? DateTime.UtcNow.Subtract(TriggeredTime);
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 报警状态
/// </summary>
public enum AlarmStatus
{
    [Description("活动")]
    Active,
    [Description("已确认")]
    Acknowledged,
    [Description("已恢复")]
    Recovered,
    [Description("已抑制")]
    Suppressed
}

/// <summary>
/// 报警统计信息
/// </summary>
public class AlarmStatistics
{
    public int TotalCount { get; set; }
    public int ActiveAlarms { get; set; }
    public int AcknowledgedAlarms { get; set; }
    public int RecoveredAlarms { get; set; }
    public int CriticalCount { get; set; }
    public int WarningCount { get; set; }
    public int InfoCount { get; set; }
    public int EmergencyCount { get; set; }
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;
    public TimeSpan Period { get; set; }
    
    public Dictionary<string, int> AlarmsByDevice { get; set; } = new();
    public Dictionary<AlarmLevel, int> AlarmsByLevel { get; set; } = new();
    public Dictionary<string, int> AlarmsByCategory { get; set; } = new();
}

/// <summary>
/// 报警事件参数
/// </summary>
public class AlarmEventArgs : EventArgs
{
    public AlarmInstance Alarm { get; set; } = new();
    public AlarmRule Rule { get; set; } = new();
    public AlarmEventType EventType { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 报警事件类型
/// </summary>
public enum AlarmEventType
{
    [Description("报警触发")]
    Triggered,
    [Description("报警确认")]
    Acknowledged,
    [Description("报警恢复")]
    Recovered,
    [Description("报警抑制")]
    Suppressed,
    [Description("报警取消抑制")]
    Unsuppressed
}
