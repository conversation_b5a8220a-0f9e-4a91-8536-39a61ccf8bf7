using Avalonia.Media;
using IOTControls;

namespace IOTPlatform.SCADA.Models;

/// <summary>
/// 趋势图数据点
/// </summary>
public class TrendDataPoint
{
    public DateTime Timestamp { get; set; }
    public double Value { get; set; }
    public string? Label { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 趋势图数据系列
/// </summary>
public class TrendDataSeries
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = "";
    public string DevicePath { get; set; } = "";
    public string Unit { get; set; } = "";
    public Color LineColor { get; set; } = Colors.Blue;
    public float LineThickness { get; set; } = 2.0f;
    public bool IsVisible { get; set; } = true;
    public bool IsSmoothCurve { get; set; } = true;
    public CurveStyle Style { get; set; } = CurveStyle.LineSegment;
    public List<TrendDataPoint> DataPoints { get; set; } = new();
    public double MinValue { get; set; } = double.MaxValue;
    public double MaxValue { get; set; } = double.MinValue;
    public double AverageValue { get; set; }
    public int DataPointCount => DataPoints.Count;
    
    /// <summary>
    /// 添加数据点
    /// </summary>
    public void AddDataPoint(TrendDataPoint point)
    {
        DataPoints.Add(point);
        UpdateStatistics();
    }
    
    /// <summary>
    /// 添加数据点
    /// </summary>
    public void AddDataPoint(DateTime timestamp, double value, string? label = null)
    {
        AddDataPoint(new TrendDataPoint
        {
            Timestamp = timestamp,
            Value = value,
            Label = label
        });
    }
    
    /// <summary>
    /// 清除数据点
    /// </summary>
    public void ClearDataPoints()
    {
        DataPoints.Clear();
        ResetStatistics();
    }
    
    /// <summary>
    /// 获取指定时间范围的数据点
    /// </summary>
    public List<TrendDataPoint> GetDataPointsInRange(DateTime startTime, DateTime endTime)
    {
        return DataPoints.Where(p => p.Timestamp >= startTime && p.Timestamp <= endTime).ToList();
    }
    
    /// <summary>
    /// 转换为IOTCurveItem
    /// </summary>
    public IOTCurveItem ToIOTCurveItem()
    {
        var sortedPoints = DataPoints.OrderBy(p => p.Timestamp).ToList();
        
        return new IOTCurveItem
        {
            Key = Id,
            Data = sortedPoints.Select(p => (float)p.Value).ToArray(),
            MarkText = sortedPoints.Select(p => p.Label ?? p.Value.ToString("F2")).ToArray(),
            LineThickness = LineThickness,
            IsSmoothCurve = IsSmoothCurve,
            LineColor = LineColor,
            Visible = IsVisible,
            Style = Style,
            RenderFormat = "{0:F2}"
        };
    }
    
    private void UpdateStatistics()
    {
        if (DataPoints.Count == 0)
        {
            ResetStatistics();
            return;
        }
        
        var values = DataPoints.Select(p => p.Value).ToArray();
        MinValue = values.Min();
        MaxValue = values.Max();
        AverageValue = values.Average();
    }
    
    private void ResetStatistics()
    {
        MinValue = double.MaxValue;
        MaxValue = double.MinValue;
        AverageValue = 0;
    }
}

/// <summary>
/// 趋势图配置
/// </summary>
public class TrendChartConfig
{
    public string Title { get; set; } = "历史数据趋势图";
    public string XAxisTitle { get; set; } = "时间";
    public string YAxisTitle { get; set; } = "数值";
    public DateTime StartTime { get; set; } = DateTime.Now.AddHours(-24);
    public DateTime EndTime { get; set; } = DateTime.Now;
    public TimeSpan RefreshInterval { get; set; } = TimeSpan.FromMinutes(1);
    public int MaxDataPoints { get; set; } = 1000;
    public bool AutoScale { get; set; } = true;
    public double? MinY { get; set; }
    public double? MaxY { get; set; }
    public bool ShowGrid { get; set; } = true;
    public bool ShowLegend { get; set; } = true;
    public bool ShowDataLabels { get; set; } = false;
    public Color BackgroundColor { get; set; } = Colors.White;
    public Color GridColor { get; set; } = Colors.LightGray;
    public List<string> TimeFormats { get; set; } = new() { "HH:mm", "MM-dd HH:mm", "yyyy-MM-dd HH:mm" };
}

/// <summary>
/// 趋势图查询参数
/// </summary>
public class TrendDataQuery
{
    public List<string> DevicePaths { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan? SampleInterval { get; set; }
    public int? MaxPoints { get; set; }
    public TrendDataAggregation Aggregation { get; set; } = TrendDataAggregation.None;
    public bool IncludeAlarms { get; set; } = false;
}

/// <summary>
/// 趋势数据聚合方式
/// </summary>
public enum TrendDataAggregation
{
    None,
    Average,
    Min,
    Max,
    Sum,
    Count
}

/// <summary>
/// 趋势图统计信息
/// </summary>
public class TrendChartStatistics
{
    public int TotalDataPoints { get; set; }
    public int TotalSeries { get; set; }
    public DateTime DataStartTime { get; set; }
    public DateTime DataEndTime { get; set; }
    public TimeSpan DataSpan => DataEndTime - DataStartTime;
    public Dictionary<string, SeriesStatistics> SeriesStats { get; set; } = new();
}

/// <summary>
/// 数据系列统计信息
/// </summary>
public class SeriesStatistics
{
    public string SeriesName { get; set; } = "";
    public int DataPointCount { get; set; }
    public double MinValue { get; set; }
    public double MaxValue { get; set; }
    public double AverageValue { get; set; }
    public double StandardDeviation { get; set; }
    public DateTime FirstDataTime { get; set; }
    public DateTime LastDataTime { get; set; }
}
