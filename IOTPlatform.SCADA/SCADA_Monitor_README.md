# SCADA监控系统使用说明

## 概述

本SCADA监控系统是基于IOTPlatform.SCADA项目开发的工业监控界面，使用了IOTControls模块的丰富工业控件，通过Prism框架集成到IOTPlatform.Desktop主窗口中。

## 功能特性

### 1. 实时监控
- **工艺流程图**：可视化显示工艺流程，包括储罐、管道、泵、阀门等设备
- **实时数据**：显示温度、压力、流量、液位等工艺参数
- **设备状态**：实时显示设备运行状态和连接状态

### 2. 设备控制
- **泵控制**：启动/停止水泵操作
- **阀门控制**：开启/关闭阀门操作
- **远程操作**：通过边缘网关进行远程设备控制

### 3. 报警管理
- **报警显示**：实时显示系统报警信息
- **报警分类**：按信息、警告、严重、紧急四个级别分类
- **报警确认**：支持报警确认操作
- **报警统计**：显示各类报警的数量统计

### 4. 数据趋势
- **历史趋势**：显示温度、压力、流量的历史趋势图
- **实时曲线**：实时更新的数据曲线显示

## 技术架构

### 使用的控件（来自IOTControls模块）
- `IOTTank`：储罐控件，显示液位
- `IOTThermometer`：温度计控件
- `IOTCircularGauge`：圆形仪表控件，显示压力
- `IOTPipeLine`：管道控件，支持流动动画
- `IOTPumpOne`：水泵控件，支持运行状态显示
- `IOTHandValve`：手动阀门控件
- `IOTIndicator`：指示器控件，显示流量
- `IOTCurve`：曲线图控件，显示趋势数据

### MVVM架构
- **View**：`SCADAMonitorView.axaml` - 用户界面
- **ViewModel**：`SCADAMonitorViewModel.cs` - 业务逻辑和数据绑定
- **Model**：使用IOTPlatform.SCADA.Models中的数据模型

### 服务集成
- **IoTGatewayService**：边缘网关通信服务
- **MrpcCommunicationService**：MQTT RPC通信服务
- **InfluxDB服务**：时序数据存储服务

## 使用方法

### 1. 启动系统
1. 运行IOTPlatform.Desktop应用程序
2. 在主菜单中选择"物联监控模块" -> "工艺流程图"
3. 系统将加载SCADA监控界面

### 2. 连接边缘网关
1. 点击顶部工具栏的"连接"按钮
2. 系统将自动连接到配置的边缘网关
3. 连接成功后，状态指示灯变为绿色，显示"已连接"

### 3. 监控操作
- **查看实时数据**：工艺参数会自动更新显示
- **控制设备**：点击设备下方的控制按钮进行操作
- **查看报警**：右侧面板显示实时报警信息
- **确认报警**：点击报警项的"确认"按钮

### 4. 数据分析
- **趋势分析**：底部显示温度、压力、流量的趋势图
- **设备状态**：右上角面板显示所有设备的状态信息

## 配置说明

### 边缘网关配置
在`appsettings.json`中配置边缘网关连接参数：
```json
{
  "EdgeGateway": {
    "ServerAddress": "localhost",
    "MrpcPort": 1883,
    "Username": "admin",
    "Password": "password",
    "PollingInterval": 1000
  }
}
```

### InfluxDB配置
配置时序数据库连接：
```json
{
  "InfluxDb": {
    "Url": "http://localhost:8086",
    "Token": "your-token",
    "Organization": "your-org",
    "Bucket": "iot-data"
  }
}
```

## 扩展开发

### 添加新的工业控件
1. 在IOTControls项目中添加新的控件
2. 在SCADAMonitorView.axaml中引用新控件
3. 在SCADAMonitorViewModel.cs中添加相应的数据绑定

### 添加新的设备类型
1. 在Models中定义新的设备模型
2. 在ViewModel中添加设备控制逻辑
3. 在View中添加设备显示和控制界面

### 自定义报警规则
1. 在AlarmInfo模型中扩展报警属性
2. 在服务层添加报警规则判断逻辑
3. 在界面中添加报警配置功能

## 故障排除

### 常见问题
1. **连接失败**：检查边缘网关配置和网络连接
2. **数据不更新**：检查设备路径配置和通信服务状态
3. **控件显示异常**：检查IOTControls项目引用和资源文件

### 日志查看
系统使用Serilog记录日志，可以在以下位置查看：
- 控制台输出
- 日志文件：`logs/iot-gateway-{Date}.log`

## 技术支持

如需技术支持，请联系开发团队或查看项目文档。
