using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 自适应刷新服务 - 性能优化
/// 根据数据变化频率动态调整刷新间隔
/// </summary>
public class AdaptiveRefreshService : IDisposable
{
    private readonly ILogger _logger;
    private readonly Timer _refreshTimer;
    private readonly object _lockObject = new();
    
    // 刷新配置
    private readonly TimeSpan _minRefreshInterval = TimeSpan.FromMilliseconds(500);  // 最小500ms
    private readonly TimeSpan _maxRefreshInterval = TimeSpan.FromSeconds(10);        // 最大10秒
    private readonly TimeSpan _defaultRefreshInterval = TimeSpan.FromSeconds(2);     // 默认2秒
    
    // 自适应算法参数
    private readonly int _changeHistorySize = 10;  // 保留最近10次变化记录
    private readonly double _highChangeThreshold = 0.7;  // 70%变化率认为是高频变化
    private readonly double _lowChangeThreshold = 0.2;   // 20%变化率认为是低频变化
    
    // 状态跟踪
    private TimeSpan _currentRefreshInterval;
    private readonly Queue<bool> _changeHistory = new();
    private DateTime _lastRefreshTime = DateTime.UtcNow;
    private int _totalRefreshCount = 0;
    private int _dataChangeCount = 0;
    private bool _isRunning = false;

    // 事件
    public event EventHandler<RefreshEventArgs>? RefreshRequested;
    public event EventHandler<RefreshIntervalChangedEventArgs>? RefreshIntervalChanged;

    public AdaptiveRefreshService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _currentRefreshInterval = _defaultRefreshInterval;
        
        // 创建定时器但不立即启动
        _refreshTimer = new Timer(OnRefreshTimer, null, Timeout.Infinite, Timeout.Infinite);
        
        _logger.Information("自适应刷新服务已创建，默认间隔: {Interval}ms", _currentRefreshInterval.TotalMilliseconds);
    }

    /// <summary>
    /// 当前刷新间隔
    /// </summary>
    public TimeSpan CurrentRefreshInterval => _currentRefreshInterval;

    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// 数据变化率
    /// </summary>
    public double ChangeRate => _totalRefreshCount > 0 ? (double)_dataChangeCount / _totalRefreshCount : 0;

    /// <summary>
    /// 启动自适应刷新
    /// </summary>
    public void Start()
    {
        lock (_lockObject)
        {
            if (_isRunning)
            {
                _logger.Warning("自适应刷新服务已在运行");
                return;
            }

            _isRunning = true;
            _refreshTimer.Change(_currentRefreshInterval, _currentRefreshInterval);
            _logger.Information("自适应刷新服务已启动");
        }
    }

    /// <summary>
    /// 停止自适应刷新
    /// </summary>
    public void Stop()
    {
        lock (_lockObject)
        {
            if (!_isRunning)
            {
                return;
            }

            _isRunning = false;
            _refreshTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.Information("自适应刷新服务已停止");
        }
    }

    /// <summary>
    /// 报告数据变化情况
    /// </summary>
    public void ReportDataChange(bool hasChanged)
    {
        lock (_lockObject)
        {
            _totalRefreshCount++;
            if (hasChanged)
            {
                _dataChangeCount++;
            }

            // 更新变化历史
            _changeHistory.Enqueue(hasChanged);
            if (_changeHistory.Count > _changeHistorySize)
            {
                _changeHistory.Dequeue();
            }

            // 每10次刷新后调整间隔
            if (_totalRefreshCount % 10 == 0)
            {
                AdjustRefreshInterval();
            }
        }
    }

    /// <summary>
    /// 手动调整刷新间隔
    /// </summary>
    public void SetRefreshInterval(TimeSpan interval)
    {
        if (interval < _minRefreshInterval)
        {
            interval = _minRefreshInterval;
        }
        else if (interval > _maxRefreshInterval)
        {
            interval = _maxRefreshInterval;
        }

        lock (_lockObject)
        {
            var oldInterval = _currentRefreshInterval;
            _currentRefreshInterval = interval;

            if (_isRunning)
            {
                _refreshTimer.Change(_currentRefreshInterval, _currentRefreshInterval);
            }

            _logger.Information("刷新间隔已手动调整: {OldInterval}ms -> {NewInterval}ms", 
                oldInterval.TotalMilliseconds, _currentRefreshInterval.TotalMilliseconds);

            RefreshIntervalChanged?.Invoke(this, new RefreshIntervalChangedEventArgs
            {
                OldInterval = oldInterval,
                NewInterval = _currentRefreshInterval,
                Reason = "手动调整"
            });
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void ResetStatistics()
    {
        lock (_lockObject)
        {
            _totalRefreshCount = 0;
            _dataChangeCount = 0;
            _changeHistory.Clear();
            _logger.Debug("刷新统计信息已重置");
        }
    }

    /// <summary>
    /// 获取刷新统计信息
    /// </summary>
    public RefreshStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new RefreshStatistics
            {
                CurrentInterval = _currentRefreshInterval,
                TotalRefreshCount = _totalRefreshCount,
                DataChangeCount = _dataChangeCount,
                ChangeRate = ChangeRate,
                IsRunning = _isRunning,
                LastRefreshTime = _lastRefreshTime,
                ChangeHistory = _changeHistory.ToArray()
            };
        }
    }

    private void OnRefreshTimer(object? state)
    {
        if (!_isRunning)
        {
            return;
        }

        try
        {
            _lastRefreshTime = DateTime.UtcNow;
            
            var eventArgs = new RefreshEventArgs
            {
                Timestamp = _lastRefreshTime,
                RefreshInterval = _currentRefreshInterval,
                RefreshCount = _totalRefreshCount
            };

            RefreshRequested?.Invoke(this, eventArgs);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "刷新定时器执行时发生异常");
        }
    }

    private void AdjustRefreshInterval()
    {
        if (_changeHistory.Count < _changeHistorySize)
        {
            return; // 数据不足，不调整
        }

        var recentChangeRate = (double)_changeHistory.Count(x => x) / _changeHistory.Count;
        var oldInterval = _currentRefreshInterval;
        var newInterval = oldInterval;
        string reason = "";

        if (recentChangeRate >= _highChangeThreshold)
        {
            // 高频变化，减少刷新间隔
            newInterval = TimeSpan.FromMilliseconds(oldInterval.TotalMilliseconds * 0.8);
            reason = $"高频变化({recentChangeRate:P1})，加快刷新";
        }
        else if (recentChangeRate <= _lowChangeThreshold)
        {
            // 低频变化，增加刷新间隔
            newInterval = TimeSpan.FromMilliseconds(oldInterval.TotalMilliseconds * 1.2);
            reason = $"低频变化({recentChangeRate:P1})，减慢刷新";
        }
        else
        {
            // 中等频率，保持当前间隔
            return;
        }

        // 限制在最小和最大间隔之间
        if (newInterval < _minRefreshInterval)
        {
            newInterval = _minRefreshInterval;
        }
        else if (newInterval > _maxRefreshInterval)
        {
            newInterval = _maxRefreshInterval;
        }

        // 只有当间隔变化超过100ms时才调整
        if (Math.Abs((newInterval - oldInterval).TotalMilliseconds) > 100)
        {
            _currentRefreshInterval = newInterval;
            _refreshTimer.Change(_currentRefreshInterval, _currentRefreshInterval);

            _logger.Information("自适应调整刷新间隔: {OldInterval}ms -> {NewInterval}ms, 原因: {Reason}", 
                oldInterval.TotalMilliseconds, _currentRefreshInterval.TotalMilliseconds, reason);

            RefreshIntervalChanged?.Invoke(this, new RefreshIntervalChangedEventArgs
            {
                OldInterval = oldInterval,
                NewInterval = _currentRefreshInterval,
                Reason = reason
            });
        }
    }

    public void Dispose()
    {
        Stop();
        _refreshTimer?.Dispose();
        _logger.Information("自适应刷新服务已释放");
    }
}

/// <summary>
/// 刷新事件参数
/// </summary>
public class RefreshEventArgs : EventArgs
{
    public DateTime Timestamp { get; set; }
    public TimeSpan RefreshInterval { get; set; }
    public int RefreshCount { get; set; }
}

/// <summary>
/// 刷新间隔变化事件参数
/// </summary>
public class RefreshIntervalChangedEventArgs : EventArgs
{
    public TimeSpan OldInterval { get; set; }
    public TimeSpan NewInterval { get; set; }
    public string Reason { get; set; } = "";
}

/// <summary>
/// 刷新统计信息
/// </summary>
public class RefreshStatistics
{
    public TimeSpan CurrentInterval { get; set; }
    public int TotalRefreshCount { get; set; }
    public int DataChangeCount { get; set; }
    public double ChangeRate { get; set; }
    public bool IsRunning { get; set; }
    public DateTime LastRefreshTime { get; set; }
    public bool[] ChangeHistory { get; set; } = Array.Empty<bool>();
}
