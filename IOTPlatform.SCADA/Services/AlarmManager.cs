using System.Collections.Concurrent;
using IOTPlatform.SCADA.Models;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 报警管理器
/// </summary>
public class AlarmManager : IDisposable
{
    private readonly ILogger _logger;
    private readonly Timer _evaluationTimer;
    private readonly ConcurrentDictionary<string, AlarmRule> _alarmRules = new();
    private readonly ConcurrentDictionary<string, AlarmInstance> _activeAlarms = new();
    private readonly ConcurrentDictionary<string, double> _deviceValues = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastEvaluationTime = new();
    private readonly object _lockObject = new();

    // 配置
    private readonly TimeSpan _evaluationInterval = TimeSpan.FromSeconds(1);
    private readonly int _maxAlarmHistory = 10000;

    // 事件
    public event EventHandler<AlarmEventArgs>? AlarmTriggered;
    public event EventHandler<AlarmEventArgs>? AlarmAcknowledged;
    public event EventHandler<AlarmEventArgs>? AlarmRecovered;
    public event EventHandler<AlarmEventArgs>? AlarmSuppressed;

    public AlarmManager(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 启动报警评估定时器
        _evaluationTimer = new Timer(EvaluateAlarms, null, _evaluationInterval, _evaluationInterval);
        
        _logger.Information("报警管理器已启动");
    }

    /// <summary>
    /// 添加报警规则
    /// </summary>
    public void AddAlarmRule(AlarmRule rule)
    {
        if (rule == null) throw new ArgumentNullException(nameof(rule));
        
        _alarmRules.AddOrUpdate(rule.Id, rule, (key, oldValue) => rule);
        _logger.Information("添加报警规则: {RuleName} ({RuleId})", rule.Name, rule.Id);
    }

    /// <summary>
    /// 移除报警规则
    /// </summary>
    public bool RemoveAlarmRule(string ruleId)
    {
        if (_alarmRules.TryRemove(ruleId, out var rule))
        {
            _logger.Information("移除报警规则: {RuleName} ({RuleId})", rule.Name, rule.Id);
            return true;
        }
        return false;
    }

    /// <summary>
    /// 获取所有报警规则
    /// </summary>
    public IEnumerable<AlarmRule> GetAlarmRules()
    {
        return _alarmRules.Values.ToArray();
    }

    /// <summary>
    /// 更新设备值
    /// </summary>
    public void UpdateDeviceValue(string devicePath, double value)
    {
        _deviceValues.AddOrUpdate(devicePath, value, (key, oldValue) => value);
    }

    /// <summary>
    /// 获取活动报警
    /// </summary>
    public IEnumerable<AlarmInstance> GetActiveAlarms()
    {
        return _activeAlarms.Values.Where(a => a.Status == AlarmStatus.Active).ToArray();
    }

    /// <summary>
    /// 获取所有报警
    /// </summary>
    public IEnumerable<AlarmInstance> GetAllAlarms()
    {
        return _activeAlarms.Values.ToArray();
    }

    /// <summary>
    /// 确认报警
    /// </summary>
    public bool AcknowledgeAlarm(string alarmId, string acknowledgedBy, string comment = "")
    {
        if (_activeAlarms.TryGetValue(alarmId, out var alarm))
        {
            if (alarm.Status == AlarmStatus.Active)
            {
                alarm.Status = AlarmStatus.Acknowledged;
                alarm.AcknowledgedTime = DateTime.UtcNow;
                alarm.AcknowledgedBy = acknowledgedBy;
                alarm.AcknowledgmentComment = comment;

                var rule = _alarmRules.Values.FirstOrDefault(r => r.Id == alarm.RuleId);
                AlarmAcknowledged?.Invoke(this, new AlarmEventArgs
                {
                    Alarm = alarm,
                    Rule = rule ?? new AlarmRule(),
                    EventType = AlarmEventType.Acknowledged
                });

                _logger.Information("报警已确认: {AlarmId} by {User}", alarmId, acknowledgedBy);
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 抑制报警
    /// </summary>
    public bool SuppressAlarm(string alarmId, string suppressedBy)
    {
        if (_activeAlarms.TryGetValue(alarmId, out var alarm))
        {
            alarm.Status = AlarmStatus.Suppressed;
            
            var rule = _alarmRules.Values.FirstOrDefault(r => r.Id == alarm.RuleId);
            AlarmSuppressed?.Invoke(this, new AlarmEventArgs
            {
                Alarm = alarm,
                Rule = rule ?? new AlarmRule(),
                EventType = AlarmEventType.Suppressed
            });

            _logger.Information("报警已抑制: {AlarmId} by {User}", alarmId, suppressedBy);
            return true;
        }
        return false;
    }

    /// <summary>
    /// 获取报警统计信息
    /// </summary>
    public AlarmStatistics GetAlarmStatistics(TimeSpan? period = null)
    {
        var alarms = _activeAlarms.Values.ToArray();
        var cutoffTime = period.HasValue ? DateTime.UtcNow.Subtract(period.Value) : DateTime.MinValue;
        
        var filteredAlarms = alarms.Where(a => a.TriggeredTime >= cutoffTime).ToArray();

        var statistics = new AlarmStatistics
        {
            TotalCount = filteredAlarms.Length,
            ActiveAlarms = filteredAlarms.Count(a => a.Status == AlarmStatus.Active),
            AcknowledgedAlarms = filteredAlarms.Count(a => a.Status == AlarmStatus.Acknowledged),
            RecoveredAlarms = filteredAlarms.Count(a => a.Status == AlarmStatus.Recovered),
            CriticalCount = filteredAlarms.Count(a => a.Level == AlarmLevel.Critical),
            WarningCount = filteredAlarms.Count(a => a.Level == AlarmLevel.Warning),
            InfoCount = filteredAlarms.Count(a => a.Level == AlarmLevel.Info),
            Period = period ?? TimeSpan.FromDays(1)
        };

        // 按设备统计
        statistics.AlarmsByDevice = filteredAlarms
            .GroupBy(a => a.DeviceName)
            .ToDictionary(g => g.Key, g => g.Count());

        // 按级别统计
        statistics.AlarmsByLevel = filteredAlarms
            .GroupBy(a => a.Level)
            .ToDictionary(g => g.Key, g => g.Count());

        return statistics;
    }

    private void EvaluateAlarms(object? state)
    {
        try
        {
            var now = DateTime.UtcNow;
            
            foreach (var rule in _alarmRules.Values.Where(r => r.IsEnabled))
            {
                EvaluateAlarmRule(rule, now);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "评估报警规则时发生异常");
        }
    }

    private void EvaluateAlarmRule(AlarmRule rule, DateTime now)
    {
        try
        {
            // 检查是否有设备值
            if (!_deviceValues.TryGetValue(rule.DevicePath, out var currentValue))
            {
                return;
            }

            // 检查延迟时间
            if (_lastEvaluationTime.TryGetValue(rule.Id, out var lastTime))
            {
                if (now.Subtract(lastTime) < rule.DelayTime)
                {
                    return;
                }
            }

            _lastEvaluationTime.AddOrUpdate(rule.Id, now, (key, oldValue) => now);

            // 评估报警条件
            bool shouldTrigger = EvaluateCondition(rule, currentValue);
            
            // 查找现有的活动报警
            var existingAlarm = _activeAlarms.Values.FirstOrDefault(a => 
                a.RuleId == rule.Id && a.Status != AlarmStatus.Recovered);

            if (shouldTrigger && existingAlarm == null)
            {
                // 触发新报警
                TriggerAlarm(rule, currentValue);
            }
            else if (!shouldTrigger && existingAlarm != null && rule.AutoRecovery)
            {
                // 自动恢复报警
                RecoverAlarm(existingAlarm);
            }
            else if (shouldTrigger && existingAlarm != null)
            {
                // 更新现有报警
                existingAlarm.CurrentValue = currentValue;
                existingAlarm.Count++;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "评估报警规则失败: {RuleId}", rule.Id);
        }
    }

    private bool EvaluateCondition(AlarmRule rule, double currentValue)
    {
        return rule.Condition switch
        {
            AlarmCondition.GreaterThan => currentValue > rule.Threshold,
            AlarmCondition.LessThan => currentValue < rule.Threshold,
            AlarmCondition.Equal => Math.Abs(currentValue - rule.Threshold) < 0.001,
            AlarmCondition.NotEqual => Math.Abs(currentValue - rule.Threshold) >= 0.001,
            AlarmCondition.InRange => rule.LowThreshold.HasValue && rule.HighThreshold.HasValue &&
                                     currentValue >= rule.LowThreshold.Value && currentValue <= rule.HighThreshold.Value,
            AlarmCondition.OutOfRange => rule.LowThreshold.HasValue && rule.HighThreshold.HasValue &&
                                        (currentValue < rule.LowThreshold.Value || currentValue > rule.HighThreshold.Value),
            _ => false
        };
    }

    private void TriggerAlarm(AlarmRule rule, double currentValue)
    {
        var alarm = new AlarmInstance
        {
            RuleId = rule.Id,
            RuleName = rule.Name,
            DevicePath = rule.DevicePath,
            DeviceName = rule.DeviceName,
            Level = rule.Level,
            Status = AlarmStatus.Active,
            Message = FormatMessage(rule.MessageTemplate, rule, currentValue),
            CurrentValue = currentValue,
            Threshold = rule.Threshold,
            TriggeredTime = DateTime.UtcNow
        };

        _activeAlarms.AddOrUpdate(alarm.Id, alarm, (key, oldValue) => alarm);
        rule.LastTriggeredTime = alarm.TriggeredTime;

        AlarmTriggered?.Invoke(this, new AlarmEventArgs
        {
            Alarm = alarm,
            Rule = rule,
            EventType = AlarmEventType.Triggered
        });

        _logger.Warning("报警触发: {RuleName} - {Message}", rule.Name, alarm.Message);
    }

    private void RecoverAlarm(AlarmInstance alarm)
    {
        alarm.Status = AlarmStatus.Recovered;
        alarm.RecoveredTime = DateTime.UtcNow;

        var rule = _alarmRules.Values.FirstOrDefault(r => r.Id == alarm.RuleId);
        AlarmRecovered?.Invoke(this, new AlarmEventArgs
        {
            Alarm = alarm,
            Rule = rule ?? new AlarmRule(),
            EventType = AlarmEventType.Recovered
        });

        _logger.Information("报警恢复: {AlarmId}", alarm.Id);
    }

    private string FormatMessage(string template, AlarmRule rule, double currentValue)
    {
        return template
            .Replace("{DeviceName}", rule.DeviceName)
            .Replace("{Condition}", rule.Condition.ToString())
            .Replace("{Threshold}", rule.Threshold.ToString("F2"))
            .Replace("{CurrentValue}", currentValue.ToString("F2"))
            .Replace("{Level}", rule.Level.ToString());
    }

    public void Dispose()
    {
        _evaluationTimer?.Dispose();
        _logger.Information("报警管理器已停止");
    }
}
