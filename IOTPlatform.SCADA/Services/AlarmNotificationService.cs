using System.Media;
using IOTPlatform.SCADA.Models;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 报警通知服务
/// </summary>
public class AlarmNotificationService : IDisposable
{
    private readonly ILogger _logger;
    private readonly Queue<AlarmNotification> _notificationQueue = new();
    private readonly Timer _processTimer;
    private readonly object _lockObject = new();
    private bool _soundEnabled = true;
    private bool _isProcessing = false;

    // 事件
    public event EventHandler<AlarmNotificationEventArgs>? NotificationSent;
    public event EventHandler<AlarmNotificationEventArgs>? NotificationFailed;

    public AlarmNotificationService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 启动通知处理定时器
        _processTimer = new Timer(ProcessNotifications, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
        
        _logger.Information("报警通知服务已启动");
    }

    /// <summary>
    /// 发送报警通知
    /// </summary>
    public async Task SendNotificationAsync(AlarmInstance alarm, AlarmRule rule)
    {
        try
        {
            var notification = new AlarmNotification
            {
                Alarm = alarm,
                Rule = rule,
                Methods = rule.NotificationMethods,
                CreatedTime = DateTime.UtcNow
            };

            lock (_lockObject)
            {
                _notificationQueue.Enqueue(notification);
            }

            _logger.Debug("报警通知已加入队列: {AlarmId}", alarm.Id);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "发送报警通知失败: {AlarmId}", alarm.Id);
        }
    }

    /// <summary>
    /// 启用/禁用声音通知
    /// </summary>
    public void SetSoundEnabled(bool enabled)
    {
        _soundEnabled = enabled;
        _logger.Information("声音通知已{Status}", enabled ? "启用" : "禁用");
    }

    /// <summary>
    /// 获取待处理通知数量
    /// </summary>
    public int GetPendingNotificationCount()
    {
        lock (_lockObject)
        {
            return _notificationQueue.Count;
        }
    }

    private void ProcessNotifications(object? state)
    {
        if (_isProcessing) return;

        try
        {
            _isProcessing = true;
            
            while (true)
            {
                AlarmNotification? notification = null;
                
                lock (_lockObject)
                {
                    if (_notificationQueue.Count == 0) break;
                    notification = _notificationQueue.Dequeue();
                }

                if (notification != null)
                {
                    ProcessNotification(notification);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理报警通知时发生异常");
        }
        finally
        {
            _isProcessing = false;
        }
    }

    private void ProcessNotification(AlarmNotification notification)
    {
        try
        {
            var methods = notification.Methods;
            var results = new List<NotificationResult>();

            // UI通知
            if (methods.HasFlag(NotificationMethod.UI))
            {
                var result = SendUINotification(notification);
                results.Add(result);
            }

            // 声音通知
            if (methods.HasFlag(NotificationMethod.Sound) && _soundEnabled)
            {
                var result = SendSoundNotification(notification);
                results.Add(result);
            }

            // 日志通知
            if (methods.HasFlag(NotificationMethod.Log))
            {
                var result = SendLogNotification(notification);
                results.Add(result);
            }

            // 邮件通知
            if (methods.HasFlag(NotificationMethod.Email))
            {
                var result = SendEmailNotification(notification);
                results.Add(result);
            }

            // 短信通知
            if (methods.HasFlag(NotificationMethod.SMS))
            {
                var result = SendSMSNotification(notification);
                results.Add(result);
            }

            // 触发通知完成事件
            var eventArgs = new AlarmNotificationEventArgs
            {
                Notification = notification,
                Results = results.ToArray(),
                Success = results.All(r => r.Success)
            };

            if (eventArgs.Success)
            {
                NotificationSent?.Invoke(this, eventArgs);
            }
            else
            {
                NotificationFailed?.Invoke(this, eventArgs);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理报警通知失败: {AlarmId}", notification.Alarm.Id);
        }
    }

    private NotificationResult SendUINotification(AlarmNotification notification)
    {
        try
        {
            // UI通知通过事件系统处理，这里只是记录
            _logger.Debug("UI通知: {Message}", notification.Alarm.Message);
            
            return new NotificationResult
            {
                Method = NotificationMethod.UI,
                Success = true,
                Message = "UI通知已发送"
            };
        }
        catch (Exception ex)
        {
            return new NotificationResult
            {
                Method = NotificationMethod.UI,
                Success = false,
                Message = ex.Message
            };
        }
    }

    private NotificationResult SendSoundNotification(AlarmNotification notification)
    {
        try
        {
            // 根据报警级别播放不同的声音
            var soundFile = GetSoundFileForLevel(notification.Alarm.Level);
            
            if (!string.IsNullOrEmpty(soundFile) && File.Exists(soundFile))
            {
                // 播放声音文件
                Task.Run(() =>
                {
                    try
                    {
                        using var player = new SoundPlayer(soundFile);
                        player.Play();
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "播放报警声音失败");
                    }
                });
            }
            else
            {
                // 使用系统默认声音
                SystemSounds.Exclamation.Play();
            }

            _logger.Debug("声音通知: {Level}", notification.Alarm.Level);
            
            return new NotificationResult
            {
                Method = NotificationMethod.Sound,
                Success = true,
                Message = "声音通知已播放"
            };
        }
        catch (Exception ex)
        {
            return new NotificationResult
            {
                Method = NotificationMethod.Sound,
                Success = false,
                Message = ex.Message
            };
        }
    }

    private NotificationResult SendLogNotification(AlarmNotification notification)
    {
        try
        {
            var logLevel = GetLogLevelForAlarm(notification.Alarm.Level);
            
            _logger.Write(logLevel, "报警通知: {DeviceName} - {Message} (级别: {Level})", 
                notification.Alarm.DeviceName, 
                notification.Alarm.Message, 
                notification.Alarm.Level);
            
            return new NotificationResult
            {
                Method = NotificationMethod.Log,
                Success = true,
                Message = "日志通知已记录"
            };
        }
        catch (Exception ex)
        {
            return new NotificationResult
            {
                Method = NotificationMethod.Log,
                Success = false,
                Message = ex.Message
            };
        }
    }

    private NotificationResult SendEmailNotification(AlarmNotification notification)
    {
        try
        {
            // 这里应该实现实际的邮件发送逻辑
            // 为了演示，我们只是记录日志
            _logger.Information("邮件通知: {Subject} - {Body}", 
                $"报警通知: {notification.Alarm.DeviceName}",
                notification.Alarm.Message);
            
            return new NotificationResult
            {
                Method = NotificationMethod.Email,
                Success = true,
                Message = "邮件通知已发送"
            };
        }
        catch (Exception ex)
        {
            return new NotificationResult
            {
                Method = NotificationMethod.Email,
                Success = false,
                Message = ex.Message
            };
        }
    }

    private NotificationResult SendSMSNotification(AlarmNotification notification)
    {
        try
        {
            // 这里应该实现实际的短信发送逻辑
            // 为了演示，我们只是记录日志
            _logger.Information("短信通知: {Message}", notification.Alarm.Message);
            
            return new NotificationResult
            {
                Method = NotificationMethod.SMS,
                Success = true,
                Message = "短信通知已发送"
            };
        }
        catch (Exception ex)
        {
            return new NotificationResult
            {
                Method = NotificationMethod.SMS,
                Success = false,
                Message = ex.Message
            };
        }
    }

    private string GetSoundFileForLevel(AlarmLevel level)
    {
        return level switch
        {
            AlarmLevel.Critical => "Assets/Sounds/critical_alarm.wav",
            AlarmLevel.Critical => "Assets/Sounds/high_alarm.wav",
            AlarmLevel.Warning => "Assets/Sounds/medium_alarm.wav",
            AlarmLevel.Info => "Assets/Sounds/low_alarm.wav",
            _ => ""
        };
    }

    private Serilog.Events.LogEventLevel GetLogLevelForAlarm(AlarmLevel level)
    {
        return level switch
        {
            AlarmLevel.Critical => Serilog.Events.LogEventLevel.Fatal,
            // AlarmLevel.Critical => Serilog.Events.LogEventLevel.Error, // 重复，已注释
            AlarmLevel.Warning => Serilog.Events.LogEventLevel.Warning,
            AlarmLevel.Info => Serilog.Events.LogEventLevel.Information,
            _ => Serilog.Events.LogEventLevel.Information
        };
    }

    public void Dispose()
    {
        _processTimer?.Dispose();
        _logger.Information("报警通知服务已停止");
    }
}

/// <summary>
/// 报警通知
/// </summary>
public class AlarmNotification
{
    public AlarmInstance Alarm { get; set; } = new();
    public AlarmRule Rule { get; set; } = new();
    public NotificationMethod Methods { get; set; }
    public DateTime CreatedTime { get; set; }
}

/// <summary>
/// 通知结果
/// </summary>
public class NotificationResult
{
    public NotificationMethod Method { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; } = "";
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 报警通知事件参数
/// </summary>
public class AlarmNotificationEventArgs : EventArgs
{
    public AlarmNotification Notification { get; set; } = new();
    public NotificationResult[] Results { get; set; } = Array.Empty<NotificationResult>();
    public bool Success { get; set; }
}
