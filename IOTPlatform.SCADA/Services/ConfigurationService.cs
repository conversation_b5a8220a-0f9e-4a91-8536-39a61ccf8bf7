using Microsoft.Extensions.Configuration;
using IOTPlatform.SCADA.Models;
using Newtonsoft.Json;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 配置管理服务
/// </summary>
public class ConfigurationService
{
    private readonly ILogger _logger;
    private SystemConfiguration _configuration;
    private readonly string _configFilePath;

    public ConfigurationService(ILogger logger, string configFilePath = "appsettings.json")
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configFilePath = configFilePath;
        LoadConfiguration();
    }

    /// <summary>
    /// 获取系统配置
    /// </summary>
    public SystemConfiguration GetConfiguration()
    {
        return _configuration;
    }

    /// <summary>
    /// 获取边缘网关配置
    /// </summary>
    public EdgeGatewayConfiguration GetEdgeGatewayConfiguration()
    {
        return _configuration.EdgeGateway;
    }

    /// <summary>
    /// 获取InfluxDB配置
    /// </summary>
    public InfluxDbConfiguration GetInfluxDbConfiguration()
    {
        return _configuration.InfluxDb;
    }

    /// <summary>
    /// 获取数据采集配置
    /// </summary>
    public DataCollectionConfiguration GetDataCollectionConfiguration()
    {
        return _configuration.DataCollection;
    }

    /// <summary>
    /// 更新配置
    /// </summary>
    public async Task UpdateConfigurationAsync(SystemConfiguration newConfiguration)
    {
        if (newConfiguration == null)
            throw new ArgumentNullException(nameof(newConfiguration));

        if (!newConfiguration.IsValid())
            throw new ArgumentException("配置无效", nameof(newConfiguration));

        try
        {
            _configuration = newConfiguration;
            await SaveConfigurationAsync();
            _logger.Information("配置更新成功");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新配置失败");
            throw;
        }
    }

    /// <summary>
    /// 重新加载配置
    /// </summary>
    public void ReloadConfiguration()
    {
        try
        {
            LoadConfiguration();
            _logger.Information("配置重新加载成功");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "重新加载配置失败");
            throw;
        }
    }

    /// <summary>
    /// 验证配置
    /// </summary>
    public bool ValidateConfiguration()
    {
        try
        {
            var isValid = _configuration.IsValid();
            if (!isValid)
            {
                _logger.Warning("配置验证失败");
            }
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "配置验证异常");
            return false;
        }
    }

    private void LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configFilePath))
            {
                var builder = new ConfigurationBuilder()
                    .AddJsonFile(_configFilePath, optional: false, reloadOnChange: true);
                var config = builder.Build();
                _configuration = new SystemConfiguration();

                // 绑定配置
                config.GetSection("EdgeGateway").Bind(_configuration.EdgeGateway);
                config.GetSection("InfluxDb").Bind(_configuration.InfluxDb);
                config.GetSection("DataCollection").Bind(_configuration.DataCollection);
                config.GetSection("Logging").Bind(_configuration.Logging);

                _logger.Information("配置文件加载成功: {ConfigFile}", _configFilePath);
            }
            else
            {
                _configuration = new SystemConfiguration();
                _logger.Warning("配置文件不存在，使用默认配置: {ConfigFile}", _configFilePath);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "加载配置文件失败: {ConfigFile}", _configFilePath);
            _configuration = new SystemConfiguration();
        }
    }

    /// <summary>
    /// 保存配置到文件（同步版本）
    /// </summary>
    public void SaveConfiguration(SystemConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        SaveConfigurationAsync().Wait();
    }

    private async Task SaveConfigurationAsync()
    {
        try
        {
            var configData = new
            {
                EdgeGateway = _configuration.EdgeGateway,
                InfluxDb = _configuration.InfluxDb,
                DataCollection = _configuration.DataCollection,
                Logging = _configuration.Logging
            };

            var json = JsonConvert.SerializeObject(configData, Formatting.Indented);
            await File.WriteAllTextAsync(_configFilePath, json);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "保存配置文件失败: {ConfigFile}", _configFilePath);
            throw;
        }
    }
}
