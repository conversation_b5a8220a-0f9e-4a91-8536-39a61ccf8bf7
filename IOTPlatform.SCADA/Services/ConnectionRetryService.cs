using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 连接重试服务 - 性能优化
/// </summary>
public class ConnectionRetryService : IDisposable
{
    private readonly ILogger _logger;
    private readonly Timer _healthCheckTimer;
    private readonly object _lockObject = new();
    
    // 重试配置
    private readonly int _maxRetryAttempts = 5;
    private readonly TimeSpan _baseRetryDelay = TimeSpan.FromSeconds(2);
    private readonly TimeSpan _maxRetryDelay = TimeSpan.FromMinutes(5);
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromSeconds(30);
    
    // 状态跟踪
    private int _currentRetryAttempt = 0;
    private DateTime _lastConnectionAttempt = DateTime.MinValue;
    private bool _isRetrying = false;
    private bool _isHealthCheckEnabled = true;

    // 事件
    public event EventHandler<ConnectionRetryEventArgs>? RetryAttemptStarted;
    public event EventHandler<ConnectionRetryEventArgs>? RetryAttemptCompleted;
    public event EventHandler<ConnectionRetryEventArgs>? MaxRetriesReached;
    public event EventHandler<HealthCheckEventArgs>? HealthCheckCompleted;

    public ConnectionRetryService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 启动健康检查定时器
        _healthCheckTimer = new Timer(PerformHealthCheck, null, _healthCheckInterval, _healthCheckInterval);
        
        _logger.Information("连接重试服务已启动");
    }

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryAttempt => _currentRetryAttempt;

    /// <summary>
    /// 是否正在重试
    /// </summary>
    public bool IsRetrying => _isRetrying;

    /// <summary>
    /// 最后连接尝试时间
    /// </summary>
    public DateTime LastConnectionAttempt => _lastConnectionAttempt;

    /// <summary>
    /// 执行带重试的连接操作
    /// </summary>
    public async Task<bool> ExecuteWithRetryAsync(Func<Task<bool>> connectionFunc, 
        CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_isRetrying)
            {
                _logger.Warning("连接重试已在进行中，跳过新的重试请求");
                return false;
            }
            _isRetrying = true;
            _currentRetryAttempt = 0;
        }

        try
        {
            while (_currentRetryAttempt < _maxRetryAttempts && !cancellationToken.IsCancellationRequested)
            {
                _currentRetryAttempt++;
                _lastConnectionAttempt = DateTime.UtcNow;

                var eventArgs = new ConnectionRetryEventArgs
                {
                    AttemptNumber = _currentRetryAttempt,
                    MaxAttempts = _maxRetryAttempts,
                    Timestamp = _lastConnectionAttempt
                };

                // 触发重试开始事件
                RetryAttemptStarted?.Invoke(this, eventArgs);
                
                _logger.Information("开始连接尝试 {Attempt}/{MaxAttempts}", _currentRetryAttempt, _maxRetryAttempts);

                try
                {
                    var success = await connectionFunc();
                    
                    eventArgs.Success = success;
                    eventArgs.CompletedAt = DateTime.UtcNow;
                    
                    // 触发重试完成事件
                    RetryAttemptCompleted?.Invoke(this, eventArgs);

                    if (success)
                    {
                        _logger.Information("连接成功，重试次数: {Attempts}", _currentRetryAttempt);
                        ResetRetryState();
                        return true;
                    }
                    else
                    {
                        _logger.Warning("连接失败，尝试次数: {Attempt}/{MaxAttempts}", _currentRetryAttempt, _maxRetryAttempts);
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "连接尝试 {Attempt} 发生异常", _currentRetryAttempt);
                    
                    eventArgs.Success = false;
                    eventArgs.Exception = ex;
                    eventArgs.CompletedAt = DateTime.UtcNow;
                    
                    RetryAttemptCompleted?.Invoke(this, eventArgs);
                }

                // 如果不是最后一次尝试，等待重试延迟
                if (_currentRetryAttempt < _maxRetryAttempts && !cancellationToken.IsCancellationRequested)
                {
                    var delay = CalculateRetryDelay(_currentRetryAttempt);
                    _logger.Information("等待 {Delay} 秒后进行下次重试", delay.TotalSeconds);
                    
                    await Task.Delay(delay, cancellationToken);
                }
            }

            // 达到最大重试次数
            _logger.Error("连接失败，已达到最大重试次数: {MaxAttempts}", _maxRetryAttempts);
            
            MaxRetriesReached?.Invoke(this, new ConnectionRetryEventArgs
            {
                AttemptNumber = _currentRetryAttempt,
                MaxAttempts = _maxRetryAttempts,
                Success = false,
                Timestamp = DateTime.UtcNow
            });

            return false;
        }
        finally
        {
            lock (_lockObject)
            {
                _isRetrying = false;
            }
        }
    }

    /// <summary>
    /// 重置重试状态
    /// </summary>
    public void ResetRetryState()
    {
        lock (_lockObject)
        {
            _currentRetryAttempt = 0;
            _isRetrying = false;
        }
        
        _logger.Debug("重试状态已重置");
    }

    /// <summary>
    /// 启用/禁用健康检查
    /// </summary>
    public void SetHealthCheckEnabled(bool enabled)
    {
        _isHealthCheckEnabled = enabled;
        _logger.Information("健康检查已{Status}", enabled ? "启用" : "禁用");
    }

    /// <summary>
    /// 手动执行健康检查
    /// </summary>
    public async Task<bool> PerformHealthCheckAsync(Func<Task<bool>> healthCheckFunc)
    {
        try
        {
            _logger.Debug("执行健康检查");
            var isHealthy = await healthCheckFunc();
            
            var eventArgs = new HealthCheckEventArgs
            {
                IsHealthy = isHealthy,
                Timestamp = DateTime.UtcNow
            };
            
            HealthCheckCompleted?.Invoke(this, eventArgs);
            
            _logger.Debug("健康检查完成，状态: {Status}", isHealthy ? "健康" : "不健康");
            return isHealthy;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "健康检查时发生异常");
            
            HealthCheckCompleted?.Invoke(this, new HealthCheckEventArgs
            {
                IsHealthy = false,
                Timestamp = DateTime.UtcNow,
                Exception = ex
            });
            
            return false;
        }
    }

    /// <summary>
    /// 获取重试统计信息
    /// </summary>
    public RetryStatistics GetRetryStatistics()
    {
        return new RetryStatistics
        {
            CurrentAttempt = _currentRetryAttempt,
            MaxAttempts = _maxRetryAttempts,
            IsRetrying = _isRetrying,
            LastAttemptTime = _lastConnectionAttempt,
            NextRetryDelay = _isRetrying ? CalculateRetryDelay(_currentRetryAttempt + 1) : TimeSpan.Zero
        };
    }

    private TimeSpan CalculateRetryDelay(int attemptNumber)
    {
        // 指数退避算法：delay = baseDelay * 2^(attemptNumber-1)
        var delay = TimeSpan.FromMilliseconds(_baseRetryDelay.TotalMilliseconds * Math.Pow(2, attemptNumber - 1));
        
        // 限制最大延迟时间
        return delay > _maxRetryDelay ? _maxRetryDelay : delay;
    }

    private void PerformHealthCheck(object? state)
    {
        if (!_isHealthCheckEnabled || _isRetrying)
        {
            return;
        }

        // 这里可以添加具体的健康检查逻辑
        // 例如：ping测试、简单的连接测试等
        _logger.Debug("定时健康检查执行");
    }

    public void Dispose()
    {
        _healthCheckTimer?.Dispose();
        _logger.Information("连接重试服务已停止");
    }
}

/// <summary>
/// 连接重试事件参数
/// </summary>
public class ConnectionRetryEventArgs : EventArgs
{
    public int AttemptNumber { get; set; }
    public int MaxAttempts { get; set; }
    public bool Success { get; set; }
    public DateTime Timestamp { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// 健康检查事件参数
/// </summary>
public class HealthCheckEventArgs : EventArgs
{
    public bool IsHealthy { get; set; }
    public DateTime Timestamp { get; set; }
    public Exception? Exception { get; set; }
}

/// <summary>
/// 重试统计信息
/// </summary>
public class RetryStatistics
{
    public int CurrentAttempt { get; set; }
    public int MaxAttempts { get; set; }
    public bool IsRetrying { get; set; }
    public DateTime LastAttemptTime { get; set; }
    public TimeSpan NextRetryDelay { get; set; }
}
