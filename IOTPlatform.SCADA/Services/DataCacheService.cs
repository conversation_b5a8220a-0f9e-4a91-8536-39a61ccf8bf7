using System.Collections.Concurrent;
using IOTPlatform.SCADA.Models;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 数据缓存服务 - 性能优化
/// </summary>
public class DataCacheService : IDisposable
{
    private readonly ILogger _logger;
    private readonly ConcurrentDictionary<string, CachedData<object>> _dataCache;
    private readonly ConcurrentDictionary<string, CachedData<IEnumerable<DeviceDataRecord>>> _historyCache;
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();

    // 缓存配置
    private readonly TimeSpan _defaultCacheExpiry = TimeSpan.FromMinutes(5);
    private readonly TimeSpan _historyCacheExpiry = TimeSpan.FromMinutes(30);
    private readonly int _maxCacheSize = 1000;

    public DataCacheService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dataCache = new ConcurrentDictionary<string, CachedData<object>>();
        _historyCache = new ConcurrentDictionary<string, CachedData<IEnumerable<DeviceDataRecord>>>();
        
        // 每5分钟清理一次过期缓存
        _cleanupTimer = new Timer(CleanupExpiredCache, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.Information("数据缓存服务已启动");
    }

    /// <summary>
    /// 获取缓存的设备数据
    /// </summary>
    public T? GetCachedData<T>(string key)
    {
        if (_dataCache.TryGetValue(key, out var cachedData))
        {
            if (!cachedData.IsExpired)
            {
                _logger.Debug("缓存命中: {Key}", key);
                if (cachedData.Data is T result) return result;
            }
            else
            {
                // 移除过期数据
                _dataCache.TryRemove(key, out _);
                _logger.Debug("缓存过期已移除: {Key}", key);
            }
        }
        
        return default(T);
    }

    /// <summary>
    /// 设置缓存数据
    /// </summary>
    public void SetCachedData<T>(string key, T data, TimeSpan? expiry = null)
    {
        var cacheExpiry = expiry ?? _defaultCacheExpiry;
        var cachedData = new CachedData<object>(data!, DateTime.UtcNow.Add(cacheExpiry));
        
        _dataCache.AddOrUpdate(key, cachedData, (k, v) => cachedData);
        
        // 检查缓存大小，如果超过限制则清理最旧的数据
        if (_dataCache.Count > _maxCacheSize)
        {
            CleanupOldestCache();
        }
        
        _logger.Debug("数据已缓存: {Key}, 过期时间: {Expiry}", key, cachedData.ExpiryTime);
    }

    /// <summary>
    /// 获取缓存的历史数据
    /// </summary>
    public IEnumerable<DeviceDataRecord>? GetCachedHistoryData(string devicePath, DateTime startTime, DateTime endTime)
    {
        var key = GenerateHistoryCacheKey(devicePath, startTime, endTime);
        
        if (_historyCache.TryGetValue(key, out var cachedData))
        {
            if (!cachedData.IsExpired)
            {
                _logger.Debug("历史数据缓存命中: {Key}", key);
                return cachedData.Data;
            }
            else
            {
                _historyCache.TryRemove(key, out _);
                _logger.Debug("历史数据缓存过期已移除: {Key}", key);
            }
        }
    
        return null;
    }
    /// <summary>
    /// 设置历史数据缓存
    /// </summary>
    public void SetCachedHistoryData(string devicePath, DateTime startTime, DateTime endTime, 
        IEnumerable<DeviceDataRecord> data)
    {
        var key = GenerateHistoryCacheKey(devicePath, startTime, endTime);
        var cachedData = new CachedData<IEnumerable<DeviceDataRecord>>(data, DateTime.UtcNow.Add(_historyCacheExpiry));
        
        _historyCache.AddOrUpdate(key, cachedData, (k, v) => cachedData);
        
        _logger.Debug("历史数据已缓存: {Key}", key);
    }

    /// <summary>
    /// 检查数据是否发生变化
    /// </summary>
    public bool HasDataChanged<T>(string key, T newData)
    {
        var cachedData = GetCachedData<T>(key);
        if (cachedData == null)
        {
            return true; // 没有缓存数据，认为是变化
        }

        // 简单的对象比较，可以根据需要实现更复杂的比较逻辑
        return !EqualityComparer<T>.Default.Equals(cachedData, newData);
    }

    /// <summary>
    /// 清除指定设备的缓存
    /// </summary>
    public void ClearDeviceCache(string devicePath)
    {
        var keysToRemove = _dataCache.Keys.Where(k => k.StartsWith($"device:{devicePath}")).ToList();
        foreach (var key in keysToRemove)
        {
            _dataCache.TryRemove(key, out _);
        }
        
        _logger.Debug("已清除设备缓存: {DevicePath}", devicePath);
    }

    /// <summary>
    /// 清除所有缓存
    /// </summary>
    public void ClearAllCache()
    {
        lock (_lockObject)
        {
            _dataCache.Clear();
            _historyCache.Clear();
            _logger.Information("已清除所有缓存");
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetCacheStatistics()
    {
        return new CacheStatistics
        {
            DataCacheCount = _dataCache.Count,
            HistoryCacheCount = _historyCache.Count,
            TotalMemoryUsage = EstimateMemoryUsage()
        };
    }

    private string GenerateHistoryCacheKey(string devicePath, DateTime startTime, DateTime endTime)
    {
        return $"history:{devicePath}:{startTime:yyyyMMddHHmm}:{endTime:yyyyMMddHHmm}";
    }

    private void CleanupExpiredCache(object? state)
    {
        try
        {
            lock (_lockObject)
            {
                // 清理过期的数据缓存
                var expiredDataKeys = _dataCache
                    .Where(kvp => kvp.Value.IsExpired)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredDataKeys)
                {
                    _dataCache.TryRemove(key, out _);
                }

                // 清理过期的历史数据缓存
                var expiredHistoryKeys = _historyCache
                    .Where(kvp => kvp.Value.IsExpired)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredHistoryKeys)
                {
                    _historyCache.TryRemove(key, out _);
                }

                if (expiredDataKeys.Count > 0 || expiredHistoryKeys.Count > 0)
                {
                    _logger.Debug("清理过期缓存: 数据缓存 {DataCount} 项, 历史缓存 {HistoryCount} 项", 
                        expiredDataKeys.Count, expiredHistoryKeys.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "清理缓存时发生异常");
        }
    }

    private void CleanupOldestCache()
    {
        try
        {
            // 移除最旧的10%缓存项
            var itemsToRemove = (int)(_dataCache.Count * 0.1);
            var oldestItems = _dataCache
                .OrderBy(kvp => kvp.Value.CreatedTime)
                .Take(itemsToRemove)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in oldestItems)
            {
                _dataCache.TryRemove(key, out _);
            }

            _logger.Debug("清理最旧缓存: {Count} 项", itemsToRemove);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "清理最旧缓存时发生异常");
        }
    }

    private long EstimateMemoryUsage()
    {
        // 简单的内存使用估算
        return (_dataCache.Count + _historyCache.Count) * 1024; // 假设每项1KB
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        ClearAllCache();
        _logger.Information("数据缓存服务已停止");
    }
}

/// <summary>
/// 缓存数据包装器
/// </summary>
public class CachedData<T>
{
    public T Data { get; }
    public DateTime CreatedTime { get; }
    public DateTime ExpiryTime { get; }
    public bool IsExpired => DateTime.UtcNow > ExpiryTime;

    public CachedData(T data, DateTime expiryTime)
    {
        Data = data;
        CreatedTime = DateTime.UtcNow;
        ExpiryTime = expiryTime;
    }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    public int DataCacheCount { get; set; }
    public int HistoryCacheCount { get; set; }
    public long TotalMemoryUsage { get; set; }
}
