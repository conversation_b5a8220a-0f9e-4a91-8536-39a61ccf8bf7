using IOTPlatform.SCADA.Models;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// IoT网关服务接口
/// </summary>
public interface IIoTGatewayService
{
    /// <summary>
    /// 是否正在运行
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<DeviceInfo> DataReceived;

    /// <summary>
    /// 报警接收事件
    /// </summary>
    event EventHandler<AlarmInfo> AlarmReceived;

    /// <summary>
    /// 状态变更事件
    /// </summary>
    event EventHandler<string> StatusChanged;

    /// <summary>
    /// 启动服务
    /// </summary>
    Task<bool> StartAsync();

    /// <summary>
    /// 停止服务
    /// </summary>
    Task StopAsync();

    /// <summary>
    /// 重启服务
    /// </summary>
    Task<bool> RestartAsync();

    /// <summary>
    /// 写入设备数据
    /// </summary>
    Task<bool> WriteDeviceDataAsync(string devicePath, object value);

    /// <summary>
    /// 读取设备数据
    /// </summary>
    Task<object> ReadDeviceDataAsync(string devicePath);

    /// <summary>
    /// 调用设备方法
    /// </summary>
    Task<string> CallDeviceMethodAsync(string devicePath, string methodName, params object[] parameters);

    /// <summary>
    /// 获取统计信息
    /// </summary>
    Task<Dictionary<string, object>> GetStatisticsAsync();

    /// <summary>
    /// 查询历史数据
    /// </summary>
    Task<IEnumerable<DeviceDataRecord>> QueryHistoryDataAsync(string devicePath, DateTime startTime, DateTime endTime);
}
