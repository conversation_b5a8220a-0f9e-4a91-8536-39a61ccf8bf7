using IOTPlatform.InfluxDbWrapper.Services;
using IOTPlatform.SCADA.Models;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// IoT网关主控制器服务
/// 整合数据采集、存储和控制功能，提供统一的服务接口
/// </summary>
public class IoTGatewayService : IIoTGatewayService, IDisposable
{
    private readonly EdgeGatewayDataService _dataService;
    private readonly TimeSeriesDataService _timeSeriesService;
    private readonly EdgeGatewayControlService _controlService;
    private readonly ConfigurationService _configService;
    private readonly ILogger _logger;
    private bool _disposed = false;
    private bool _isRunning = false;

    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning => _isRunning;

    // 统计信息
    private long _totalDataPointsCollected = 0;
    private long _totalAlarmsReceived = 0;
    private DateTime _startTime;
    private readonly object _statsLock = new object();
    // 实现IIoTGatewayService接口的事件
    public event EventHandler<DeviceInfo> DataReceived;
    public event EventHandler<AlarmInfo> AlarmReceived;
    public event EventHandler<string> StatusChanged;
    
    // 内部事件
    public event Action<IoTDataPoint> DataPointReceived;
    public event Action<Exception> ErrorOccurred;
    public IoTGatewayService(
        EdgeGatewayDataService dataService,
        TimeSeriesDataService timeSeriesService,
        EdgeGatewayControlService controlService,
        ConfigurationService configService,
        ILogger logger)
    {
        _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
        _timeSeriesService = timeSeriesService ?? throw new ArgumentNullException(nameof(timeSeriesService));
        _controlService = controlService ?? throw new ArgumentNullException(nameof(controlService));
        _configService = configService ?? throw new ArgumentNullException(nameof(configService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        InitializeEventHandlers();
    }

    /// <summary>
    /// 是否正在运行
    /// </summary>

    /// <summary>
    /// 获取运行统计信息
    /// </summary>
    public IoTGatewayStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            var uptime = _isRunning ? DateTime.UtcNow - _startTime : TimeSpan.Zero;
            
            return new IoTGatewayStatistics
            {
                IsRunning = _isRunning,
                StartTime = _startTime,
                Uptime = uptime,
                TotalDataPointsCollected = _totalDataPointsCollected,
                TotalAlarmsReceived = _totalAlarmsReceived,
                DataPointsPerMinute = uptime.TotalMinutes > 0 ? _totalDataPointsCollected / uptime.TotalMinutes : 0,
                AlarmsPerMinute = uptime.TotalMinutes > 0 ? _totalAlarmsReceived / uptime.TotalMinutes : 0
            };
        }
    }

    /// <summary>
    /// 启动IoT网关服务
    /// </summary>
    public async Task<bool> StartAsync()
    {
        if (_isRunning)
        {
            _logger.Warning("IoT网关服务已在运行中");
            return true;
        }

        try
        {
            _logger.Information("正在启动IoT网关服务...");
            StatusChanged?.Invoke(this, "正在启动...");

            // 1. 验证配置
            if (!_configService.ValidateConfiguration())
            {
                throw new InvalidOperationException("配置验证失败");
            }

            var config = _configService.GetConfiguration();

            // 2. 测试InfluxDB连接
            StatusChanged?.Invoke(this, "测试数据库连接...");
            var dbConnected = await _timeSeriesService.TestConnectionAsync();
            if (!dbConnected)
            {
                throw new InvalidOperationException("无法连接到InfluxDB");
            }

            // 3. 连接边缘网关
            StatusChanged?.Invoke(this, "连接边缘网关...");
            var gatewayConnected = await _dataService.ConnectAsync(config.EdgeGateway);
            if (!gatewayConnected)
            {
                throw new InvalidOperationException("无法连接到边缘网关");
            }

            // 4. 配置数据采集
            StatusChanged?.Invoke(this, "配置数据采集...");
            _dataService.AddDevicePaths(config.DataCollection.DevicePaths);
            _timeSeriesService.ConfigureBatchWrite(
                config.DataCollection.BatchSize, 
                config.DataCollection.BatchInterval);

            // 5. 启动数据轮询
            StatusChanged?.Invoke(this, "启动数据轮询...");
            _dataService.StartPolling(config.EdgeGateway.PollingInterval);

            // 6. 标记为运行状态
            _isRunning = true;
            _startTime = DateTime.UtcNow;

            _logger.Information("IoT网关服务启动成功");
            StatusChanged?.Invoke(this, "运行中");

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "启动IoT网关服务失败");
            StatusChanged?.Invoke(this, $"启动失败: {ex.Message}");
            ErrorOccurred?.Invoke(ex);
            
            // 清理资源
            await StopAsync();
            return false;
        }
    }

    /// <summary>
    /// 停止IoT网关服务
    /// </summary>
    public async Task StopAsync()
    {
        if (!_isRunning)
        {
            _logger.Information("IoT网关服务未在运行");
            return;
        }

        try
        {
            _logger.Information("正在停止IoT网关服务...");
            StatusChanged?.Invoke(this, "正在停止...");

            // 1. 停止数据轮询
            _dataService.StopPolling();

            // 2. 写入剩余数据
            StatusChanged?.Invoke(this, "写入剩余数据...");
            await _timeSeriesService.FlushAllAsync();

            // 3. 断开网关连接
            _dataService.Disconnect();

            // 4. 标记为停止状态
            _isRunning = false;

            _logger.Information("IoT网关服务已停止");
            StatusChanged?.Invoke(this, "已停止");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "停止IoT网关服务异常");
            StatusChanged?.Invoke(this, $"停止异常: {ex.Message}");
            ErrorOccurred?.Invoke(ex);
        }
    }

    /// <summary>
    /// 重启IoT网关服务
    /// </summary>
    public async Task<bool> RestartAsync()
    {
        _logger.Information("正在重启IoT网关服务...");
        
        await StopAsync();
        await Task.Delay(2000); // 等待2秒
        
        return await StartAsync();
    }

    /// <summary>
    /// 添加设备路径
    /// </summary>
    public void AddDevicePath(string devicePath)
    {
        _dataService.AddDevicePath(devicePath);
        
        // 更新配置
        var config = _configService.GetConfiguration();
        if (!config.DataCollection.DevicePaths.Contains(devicePath))
        {
            config.DataCollection.DevicePaths.Add(devicePath);
            _ = Task.Run(async () => await _configService.UpdateConfigurationAsync(config));
        }
    }

    /// <summary>
    /// 移除设备路径
    /// </summary>
    public void RemoveDevicePath(string devicePath)
    {
        _dataService.RemoveDevicePath(devicePath);
        
        // 更新配置
        var config = _configService.GetConfiguration();
        if (config.DataCollection.DevicePaths.Remove(devicePath))
        {
            _ = Task.Run(async () => await _configService.UpdateConfigurationAsync(config));
        }
    }

    /// <summary>
    /// 获取所有设备路径
    /// </summary>
    public List<string> GetDevicePaths()
    {
        return _dataService.GetDevicePaths();
    }

    /// <summary>
    /// 写入设备数据
    /// </summary>
    public async Task<bool> WriteDeviceDataAsync(string devicePath, object value)
    {
        if (!_isRunning)
            throw new InvalidOperationException("IoT网关服务未运行");

        return await _controlService.WriteDeviceDataAsync(devicePath, value);
    }

    /// <summary>
    /// 调用设备方法
    /// </summary>
    public async Task<string> CallDeviceMethodAsync(string devicePath, string methodName, params object[] parameters)
    {
        if (!_isRunning)
            throw new InvalidOperationException("IoT网关服务未运行");

        return await _controlService.CallDeviceMethodAsync(devicePath, methodName, parameters);
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    public async Task<DeviceStatus> GetDeviceStatusAsync(string devicePath)
    {
        if (!_isRunning)
            throw new InvalidOperationException("IoT网关服务未运行");

        return await _controlService.GetDeviceStatusAsync(devicePath);
    }

    /// <summary>
    /// 查询历史数据
    /// </summary>
    public async Task<IEnumerable<DeviceDataRecord>> QueryHistoryDataAsync(string devicePath, DateTime startTime, DateTime endTime)
    {
        try
        {
            _logger.Information("查询历史数据: {DevicePath} from {StartTime} to {EndTime}", devicePath, startTime, endTime);
            
            // 调用时序数据服务查询历史数据
            var dataPoints = await _timeSeriesService.QueryDeviceDataAsync(devicePath, startTime, endTime);
            
            // 转换IoTDataPoint到DeviceDataRecord
            var records = dataPoints.Select(dp => new DeviceDataRecord
            {
                DeviceId = dp.DeviceId,
                DeviceName = dp.DeviceName,
                Value = dp.Value,
                Timestamp = dp.Timestamp,
                Quality = dp.Quality,
                Status = dp.Status
            });
            
            return records;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "查询历史数据失败: {DevicePath}", devicePath);
            throw;
        }
    }
    /// <summary>
    /// 查询历史报警
    /// </summary>
    public async Task<IEnumerable<object>> QueryHistoryAlarmsAsync(string deviceId, DateTime start, DateTime end)
    {
        return await _timeSeriesService.QueryAlarmsAsync(deviceId, start, end);
    }

    private void InitializeEventHandlers()
    {
        // 数据接收事件
        _dataService.DataReceived += OnDataReceived;
        _dataService.AlarmReceived += OnAlarmReceived;
        _dataService.ErrorOccurred += OnErrorOccurred;
    }

    private void OnDataReceived(IoTDataPoint dataPoint)
    {
        try
        {
            // 写入时序数据库
            _timeSeriesService.EnqueueDataPoint(dataPoint);
            
            // 更新统计
            lock (_statsLock)
            {
                _totalDataPointsCollected++;
            }

            // 触发事件
            DataPointReceived?.Invoke(dataPoint);
            
            _logger.Debug("数据点已处理: {DeviceId} = {Value}", dataPoint.DeviceId, dataPoint.Value);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理数据点异常: {DeviceId}", dataPoint?.DeviceId);
            ErrorOccurred?.Invoke(ex);
        }
    }

    private void OnAlarmReceived(AlarmInfo alarm)
    {
        try
        {
            // 写入时序数据库
            _timeSeriesService.EnqueueAlarm(alarm);
            
            // 更新统计
            lock (_statsLock)
            {
                _totalAlarmsReceived++;
            }

            // 触发事件
            AlarmReceived?.Invoke(this, alarm);
            
            _logger.Information("报警已处理: {DeviceId} - {Message}", alarm.DeviceId, alarm.Message);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理报警异常: {DeviceId}", alarm?.DeviceId);
            ErrorOccurred?.Invoke(ex);
        }
    }

    private void OnErrorOccurred(Exception ex)
    {
        _logger.Error(ex, "数据采集服务异常");
        ErrorOccurred?.Invoke(ex);
    }


    /// <summary>
    /// 读取设备数据
    /// </summary>
    public async Task<object> ReadDeviceDataAsync(string devicePath)
    {
        try
        {
            if (!_isRunning)
                throw new InvalidOperationException("服务未运行");

            _logger.Information("读取设备数据: {DevicePath}", devicePath);
            
            // 这里应该调用实际的设备读取逻辑
            // 暂时返回模拟数据
            await Task.Delay(100); // 模拟网络延迟
            return new Random().NextDouble() * 100;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "读取设备数据失败: {DevicePath}", devicePath);
            throw;
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    public async Task<Dictionary<string, object>> GetStatisticsAsync()
    {
        await Task.CompletedTask;
        
        lock (_statsLock)
        {
            var uptime = _isRunning ? DateTime.Now - _startTime : TimeSpan.Zero;
            var uptimeMinutes = uptime.TotalMinutes;
            
            return new Dictionary<string, object>
            {
                ["IsRunning"] = _isRunning,
                ["StartTime"] = _startTime,
                ["Uptime"] = uptime,
                ["TotalDataPointsCollected"] = _totalDataPointsCollected,
                ["TotalAlarmsReceived"] = _totalAlarmsReceived,
                ["DataPointsPerMinute"] = uptimeMinutes > 0 ? _totalDataPointsCollected / uptimeMinutes : 0,
                ["AlarmsPerMinute"] = uptimeMinutes > 0 ? _totalAlarmsReceived / uptimeMinutes : 0
            };
        }
    }

    /// <summary>
    /// 查询历史数据
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopAsync().Wait(TimeSpan.FromSeconds(10));
            
            _dataService?.Dispose();
            _timeSeriesService?.Dispose();
            _controlService?.Dispose();
            
            _disposed = true;
        }
    }
}

/// <summary>
/// IoT网关统计信息
/// </summary>
public class IoTGatewayStatistics
{
    public bool IsRunning { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
    public long TotalDataPointsCollected { get; set; }
    public long TotalAlarmsReceived { get; set; }
    public double DataPointsPerMinute { get; set; }
    public double AlarmsPerMinute { get; set; }
}
