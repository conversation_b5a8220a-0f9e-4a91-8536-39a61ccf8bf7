using HslCommunication.MQTT;
using IOTPlatform.SCADA.Models;
using Newtonsoft.Json;
using Serilog;

namespace IOTPlatform.SCADA.Services;

public class MrpcCommunicationService : IDisposable
{
    private MqttRpcDevice _mqttRpcClient;
    private readonly object _lockObject = new object();
    private bool _isConnected;
    private readonly ILogger _logger;

    public event Action<bool> ConnectionStatusChanged;

    public MrpcCommunicationService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public bool IsConnected
    {
        get
        {
            lock (_lockObject)
            {
                return _isConnected;
            }
        }
        private set
        {
            lock (_lockObject)
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    ConnectionStatusChanged?.Invoke(value);
                }
            }
        }
    }

    public async Task<bool> ConnectAsync(string serverIp, int port, string clientId = null)
    {
        try
        {
            _mqttRpcClient = new MqttRpcDevice(serverIp, port, clientId ?? $"SCADA_{Guid.NewGuid():N}");
            _mqttRpcClient.ConnectServer();
            IsConnected = true;
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接失败: {ex.Message}");
            IsConnected = false;
            return false;
        }
    }

    public async Task<T> ReadDeviceDataAsync<T>(string devicePath)
    {
        if (!IsConnected) throw new InvalidOperationException("未连接到网关");

        try
        {
            var result = await Task.Run(() => _mqttRpcClient.ReadString(devicePath, 0));
            if (result.IsSuccess)
            {
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)result.Content;
                }
                else
                {
                    return JsonConvert.DeserializeObject<T>(result.Content);
                }
            }
            throw new Exception($"读取数据失败: {result.Message}");
        }
        catch (Exception ex)
        {
            throw new Exception($"读取设备数据异常: {ex.Message}");
        }
    }

    public async Task<bool> WriteDeviceDataAsync<T>(string devicePath, T value)
    {
        if (!IsConnected) throw new InvalidOperationException("未连接到网关");

        try
        {
            // 假设有 Write 方法，参数需根据实际 API 调整
            var result = await Task.Run(() => _mqttRpcClient.Write(devicePath, value.ToString()));
            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"写入数据异常: {ex.Message}");
            return false;
        }
    }

    public async Task<List<AlarmInfo>> GetAlarmsAsync()
    {
        if (!IsConnected) throw new InvalidOperationException("未连接到网关");

        try
        {
            var result = await Task.Run(() => _mqttRpcClient.ReadString("Business/GetAlarms", 0));
            if (result.IsSuccess)
            {
                return JsonConvert.DeserializeObject<List<AlarmInfo>>(result.Content) ?? new List<AlarmInfo>();
            }
            return new List<AlarmInfo>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取报警信息异常: {ex.Message}");
            return new List<AlarmInfo>();
        }
    }

    public async Task<AlarmStatistics> GetAlarmStatisticsAsync()
    {
        if (!IsConnected) throw new InvalidOperationException("未连接到网关");

        try
        {
            var result = await Task.Run(() => _mqttRpcClient.ReadString("Business/GetAlarmJsonCount", 0));
            if (result.IsSuccess)
            {
                return JsonConvert.DeserializeObject<AlarmStatistics>(result.Content) ?? new AlarmStatistics();
            }
            return new AlarmStatistics();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取报警统计异常: {ex.Message}");
            return new AlarmStatistics();
        }
    }

    public async Task<string> CallDeviceMethodAsync(string devicePath, string methodName, params object[] parameters)
    {
        if (!IsConnected) throw new InvalidOperationException("未连接到网关");

        try
        {
            // 需根据实际 API 调整参数
            var result = await Task.Run(() => _mqttRpcClient.ReadString(devicePath, 0));
            return result.IsSuccess ? result.Content : result.Message;
        }
        catch (Exception ex)
        {
            return $"调用方法异常: {ex.Message}";
        }
    }


    /// <summary>
    /// 断开连接
    /// </summary>
    public void Disconnect()
    {
        try
        {
            _mqttRpcClient?.ConnectClose();
            _logger.Information("MQTT RPC 连接已断开");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "断开 MQTT RPC 连接时发生错误");
        }
    }
    public void Dispose()
    {
        try
        {
            _mqttRpcClient?.ConnectClose();
            _mqttRpcClient?.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"释放资源异常: {ex.Message}");
        }
    }
}