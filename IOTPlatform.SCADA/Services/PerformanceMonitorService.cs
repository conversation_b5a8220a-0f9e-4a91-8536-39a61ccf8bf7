using System.Diagnostics;
using Serilog;

namespace IOTPlatform.SCADA.Services;

/// <summary>
/// 性能监控服务 - 监控系统性能指标
/// </summary>
public class PerformanceMonitorService : IDisposable
{
    private readonly ILogger _logger;
    private readonly Timer _monitorTimer;
    private readonly Process _currentProcess;
    private readonly object _lockObject = new();
    
    // 监控配置
    private readonly TimeSpan _monitorInterval = TimeSpan.FromSeconds(30);
    private readonly int _historySize = 100;
    
    // 性能指标历史记录
    private readonly Queue<PerformanceMetrics> _metricsHistory = new();
    private PerformanceMetrics? _lastMetrics;
    private DateTime _startTime = DateTime.UtcNow;
    
    // 阈值配置
    private readonly double _cpuWarningThreshold = 80.0;  // CPU使用率警告阈值
    private readonly long _memoryWarningThreshold = 500 * 1024 * 1024; // 内存使用警告阈值 (500MB)
    private readonly int _responseTimeWarningThreshold = 1000; // 响应时间警告阈值 (1秒)

    // 事件
    public event EventHandler<PerformanceMetrics>? MetricsUpdated;
    public event EventHandler<PerformanceAlertEventArgs>? PerformanceAlert;

    public PerformanceMonitorService(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _currentProcess = Process.GetCurrentProcess();
        
        // 启动监控定时器
        _monitorTimer = new Timer(CollectMetrics, null, TimeSpan.Zero, _monitorInterval);
        
        _logger.Information("性能监控服务已启动，监控间隔: {Interval}秒", _monitorInterval.TotalSeconds);
    }

    /// <summary>
    /// 获取当前性能指标
    /// </summary>
    public PerformanceMetrics? CurrentMetrics => _lastMetrics;

    /// <summary>
    /// 获取性能指标历史记录
    /// </summary>
    public PerformanceMetrics[] GetMetricsHistory()
    {
        lock (_lockObject)
        {
            return _metricsHistory.ToArray();
        }
    }

    /// <summary>
    /// 获取性能统计摘要
    /// </summary>
    public PerformanceSummary GetPerformanceSummary()
    {
        lock (_lockObject)
        {
            if (_metricsHistory.Count == 0)
            {
                return new PerformanceSummary();
            }

            var metrics = _metricsHistory.ToArray();
            
            return new PerformanceSummary
            {
                StartTime = _startTime,
                Duration = DateTime.UtcNow - _startTime,
                SampleCount = metrics.Length,
                
                AverageCpuUsage = metrics.Average(m => m.CpuUsage),
                MaxCpuUsage = metrics.Max(m => m.CpuUsage),
                MinCpuUsage = metrics.Min(m => m.CpuUsage),
                
                AverageMemoryUsage = metrics.Average(m => m.MemoryUsage),
                MaxMemoryUsage = metrics.Max(m => m.MemoryUsage),
                MinMemoryUsage = metrics.Min(m => m.MemoryUsage),
                
                AverageResponseTime = metrics.Average(m => m.ResponseTime),
                MaxResponseTime = metrics.Max(m => m.ResponseTime),
                MinResponseTime = metrics.Min(m => m.ResponseTime),
                
                TotalDataPoints = metrics.Sum(m => m.DataPointsProcessed),
                TotalErrors = metrics.Sum(m => m.ErrorCount),
                
                CurrentMetrics = _lastMetrics
            };
        }
    }

    /// <summary>
    /// 记录数据处理性能
    /// </summary>
    public void RecordDataProcessing(int dataPointCount, TimeSpan processingTime)
    {
        if (_lastMetrics != null)
        {
            _lastMetrics.DataPointsProcessed += dataPointCount;
            _lastMetrics.ResponseTime = Math.Max(_lastMetrics.ResponseTime, (int)processingTime.TotalMilliseconds);
        }
    }

    /// <summary>
    /// 记录错误
    /// </summary>
    public void RecordError()
    {
        if (_lastMetrics != null)
        {
            _lastMetrics.ErrorCount++;
        }
    }

    /// <summary>
    /// 重置性能统计
    /// </summary>
    public void ResetStatistics()
    {
        lock (_lockObject)
        {
            _metricsHistory.Clear();
            _startTime = DateTime.UtcNow;
            _logger.Information("性能统计已重置");
        }
    }

    private void CollectMetrics(object? state)
    {
        try
        {
            var metrics = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                CpuUsage = GetCpuUsage(),
                MemoryUsage = GetMemoryUsage(),
                ThreadCount = _currentProcess.Threads.Count,
                HandleCount = _currentProcess.HandleCount,
                ResponseTime = 0, // 将在数据处理时更新
                DataPointsProcessed = 0, // 将在数据处理时更新
                ErrorCount = 0 // 将在错误发生时更新
            };

            lock (_lockObject)
            {
                // 保存指标
                _metricsHistory.Enqueue(metrics);
                if (_metricsHistory.Count > _historySize)
                {
                    _metricsHistory.Dequeue();
                }
                
                _lastMetrics = metrics;
            }

            // 触发指标更新事件
            MetricsUpdated?.Invoke(this, metrics);

            // 检查性能警告
            CheckPerformanceAlerts(metrics);

            _logger.Debug("性能指标已收集: CPU={CpuUsage:F1}%, 内存={MemoryUsage}MB, 线程={ThreadCount}", 
                metrics.CpuUsage, metrics.MemoryUsage / (1024 * 1024), metrics.ThreadCount);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "收集性能指标时发生异常");
        }
    }

    private double GetCpuUsage()
    {
        try
        {
            // 简化的CPU使用率计算
            // 在实际应用中，可能需要更精确的计算方法
            return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.ProcessorCount / 1000.0 * 100;
        }
        catch
        {
            return 0;
        }
    }

    private long GetMemoryUsage()
    {
        try
        {
            return _currentProcess.WorkingSet64;
        }
        catch
        {
            return 0;
        }
    }

    private void CheckPerformanceAlerts(PerformanceMetrics metrics)
    {
        var alerts = new List<string>();

        // 检查CPU使用率
        if (metrics.CpuUsage > _cpuWarningThreshold)
        {
            alerts.Add($"CPU使用率过高: {metrics.CpuUsage:F1}%");
        }

        // 检查内存使用
        if (metrics.MemoryUsage > _memoryWarningThreshold)
        {
            alerts.Add($"内存使用过高: {metrics.MemoryUsage / (1024 * 1024):F0}MB");
        }

        // 检查响应时间
        if (metrics.ResponseTime > _responseTimeWarningThreshold)
        {
            alerts.Add($"响应时间过长: {metrics.ResponseTime}ms");
        }

        // 触发警告事件
        if (alerts.Count > 0)
        {
            var alertArgs = new PerformanceAlertEventArgs
            {
                Timestamp = metrics.Timestamp,
                Metrics = metrics,
                Alerts = alerts.ToArray()
            };

            PerformanceAlert?.Invoke(this, alertArgs);
            
            _logger.Warning("性能警告: {Alerts}", string.Join(", ", alerts));
        }
    }

    public void Dispose()
    {
        _monitorTimer?.Dispose();
        _currentProcess?.Dispose();
        _logger.Information("性能监控服务已停止");
    }
}

/// <summary>
/// 性能指标
/// </summary>
public class PerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public double CpuUsage { get; set; }
    public long MemoryUsage { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public int ResponseTime { get; set; }
    public int DataPointsProcessed { get; set; }
    public int ErrorCount { get; set; }
}

/// <summary>
/// 性能统计摘要
/// </summary>
public class PerformanceSummary
{
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public int SampleCount { get; set; }
    
    public double AverageCpuUsage { get; set; }
    public double MaxCpuUsage { get; set; }
    public double MinCpuUsage { get; set; }
    
    public double AverageMemoryUsage { get; set; }
    public long MaxMemoryUsage { get; set; }
    public long MinMemoryUsage { get; set; }
    
    public double AverageResponseTime { get; set; }
    public int MaxResponseTime { get; set; }
    public int MinResponseTime { get; set; }
    
    public long TotalDataPoints { get; set; }
    public long TotalErrors { get; set; }
    
    public PerformanceMetrics? CurrentMetrics { get; set; }
}

/// <summary>
/// 性能警告事件参数
/// </summary>
public class PerformanceAlertEventArgs : EventArgs
{
    public DateTime Timestamp { get; set; }
    public PerformanceMetrics Metrics { get; set; } = new();
    public string[] Alerts { get; set; } = Array.Empty<string>();
}
