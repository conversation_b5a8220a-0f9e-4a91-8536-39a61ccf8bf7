using Microsoft.VisualStudio.TestTools.UnitTesting;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using Moq;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;

namespace IOTPlatform.SCADA.Tests;

[TestClass]
public class BasicServiceTests
{
    private Mock<ILogger> _mockLogger;

    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger>();
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithValidData_ShouldBeValid()
    {
        // Arrange
        var config = new InfluxDbConfiguration
        {
            Url = "http://localhost:8086",
            Token = "test-token",
            Organization = "test-org",
            Bucket = "test-bucket"
        };

        // Act
        var isValid = config.IsValid();

        // Assert
        Assert.IsTrue(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyUrl_ShouldBeInvalid()
    {
        // Arrange
        var config = new InfluxDbConfiguration
        {
            Url = "",
            Token = "test-token",
            Organization = "test-org",
            Bucket = "test-bucket"
        };

        // Act
        var isValid = config.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void IoTDataPoint_CreateMethod_ShouldCreateValidDataPoint()
    {
        // Arrange
        var deviceId = "device-001";
        var deviceName = "Temperature Sensor";
        var value = 25.5;

        // Act
        var dataPoint = IoTDataPoint.Create(deviceId, deviceName, value);

        // Assert
        Assert.IsNotNull(dataPoint);
        Assert.AreEqual(deviceId, dataPoint.DeviceId);
        Assert.AreEqual(deviceName, dataPoint.DeviceName);
        Assert.AreEqual(value, dataPoint.Value);
        Assert.AreEqual("Online", dataPoint.Status);
        Assert.AreEqual(100, dataPoint.Quality);
    }

    [TestMethod]
    public void AlarmInfo_Properties_ShouldSetCorrectly()
    {
        // Arrange
        var alarm = new AlarmInfo();

        // Act
        alarm.Id = "alarm-001";
        alarm.DeviceId = "device-001";
        alarm.Message = "Test alarm";
        alarm.Level = AlarmLevel.Warning;
        alarm.Timestamp = DateTime.UtcNow;

        // Assert
        Assert.AreEqual("alarm-001", alarm.Id);
        Assert.AreEqual("device-001", alarm.DeviceId);
        Assert.AreEqual("Test alarm", alarm.Message);
        Assert.AreEqual(AlarmLevel.Warning, alarm.Level);
        Assert.IsFalse(alarm.IsAcknowledged);
    }

    [TestMethod]
    public void AlarmStatistics_Properties_ShouldSetCorrectly()
    {
        // Arrange
        var stats = new AlarmStatistics();

        // Act
        stats.TotalCount = 10;
        stats.InfoCount = 3;
        stats.WarningCount = 4;
        stats.CriticalCount = 2;
        stats.EmergencyCount = 1;

        // Assert
        Assert.AreEqual(10, stats.TotalCount);
        Assert.AreEqual(3, stats.InfoCount);
        Assert.AreEqual(4, stats.WarningCount);
        Assert.AreEqual(2, stats.CriticalCount);
        Assert.AreEqual(1, stats.EmergencyCount);
    }

    [TestMethod]
    public void DeviceInfo_Properties_ShouldSetCorrectly()
    {
        // Arrange
        var device = new DeviceInfo();

        // Act
        device.Name = "Test Device";
        device.Path = "test/path";
        device.IsOnline = true;
        device.Value = 42.0;
        device.Status = "Running";

        // Assert
        Assert.AreEqual("Test Device", device.Name);
        Assert.AreEqual("test/path", device.Path);
        Assert.IsTrue(device.IsOnline);
        Assert.AreEqual(42.0, device.Value);
        Assert.AreEqual("Running", device.Status);
    }

    [TestMethod]
    public void ConfigurationService_WithValidLogger_ShouldCreateInstance()
    {
        // Arrange & Act
        var service = new ConfigurationService(_mockLogger.Object);

        // Assert
        Assert.IsNotNull(service);
        var config = service.GetConfiguration();
        Assert.IsNotNull(config);
        Assert.IsNotNull(config.EdgeGateway);
        Assert.IsNotNull(config.InfluxDb);
        Assert.IsNotNull(config.DataCollection);
    }

    [TestMethod]
    public void EdgeGatewayConfiguration_DefaultValues_ShouldBeSet()
    {
        // Arrange & Act
        var config = new EdgeGatewayConfiguration();

        // Assert
        Assert.IsNotNull(config.ServerAddress);
        Assert.IsTrue(config.MrpcPort > 0);
        Assert.IsTrue(config.PollingInterval > 0);
    }

    [TestMethod]
    public void DataCollectionConfiguration_DefaultValues_ShouldBeSet()
    {
        // Arrange & Act
        var config = new DataCollectionConfiguration();

        // Assert
        Assert.IsNotNull(config.DevicePaths);
        Assert.IsTrue(config.BatchSize > 0);
        Assert.IsTrue(config.BatchInterval > 0);
    }

    [TestMethod]
    public void SystemConfiguration_DefaultValues_ShouldBeSet()
    {
        // Arrange & Act
        var config = new SystemConfiguration();

        // Assert
        Assert.IsNotNull(config.EdgeGateway);
        Assert.IsNotNull(config.InfluxDb);
        Assert.IsNotNull(config.DataCollection);
        Assert.IsNotNull(config.Logging);
    }
}
