using Microsoft.VisualStudio.TestTools.UnitTesting;
using IOTPlatform.SCADA.Services;
using IOTPlatform.SCADA.Models;
using Moq;
using Serilog;
using System;
using System.IO;

namespace IOTPlatform.SCADA.Tests;

[TestClass]
public class ConfigurationServiceTests
{
    private Mock<ILogger> _mockLogger;
    private string _testConfigPath;
    private ConfigurationService _configurationService;

    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger>();
        _testConfigPath = Path.Combine(Path.GetTempPath(), "test-config.json");
        
        // 创建测试配置文件
        CreateTestConfigFile();
        
        _configurationService = new ConfigurationService(_mockLogger.Object, _testConfigPath);
    }

    [TestCleanup]
    public void Cleanup()
    {
        // 清理测试文件
        if (File.Exists(_testConfigPath))
        {
            File.Delete(_testConfigPath);
        }
        
        
    }

    [TestMethod]
    public void ConfigurationService_WithValidConfigFile_ShouldLoadConfiguration()
    {
        // Act
        var config = _configurationService.GetConfiguration();

        // Assert
        Assert.IsNotNull(config);
        Assert.IsNotNull(config.EdgeGateway);
        Assert.IsNotNull(config.InfluxDb);
    }

    [TestMethod]
    public void ConfigurationService_ValidateConfiguration_WithValidConfig_ShouldReturnTrue()
    {
        // Act
        var isValid = _configurationService.ValidateConfiguration();

        // Assert
        Assert.IsTrue(isValid);
    }

    [TestMethod]
    public void ConfigurationService_WithNonExistentFile_ShouldCreateDefaultConfiguration()
    {
        // Arrange
        var nonExistentPath = Path.Combine(Path.GetTempPath(), "non-existent-config.json");
        
        // Act
        var service = new ConfigurationService(_mockLogger.Object, nonExistentPath);
        var config = service.GetConfiguration();

        // Assert
        Assert.IsNotNull(config);
        Assert.IsNotNull(config.EdgeGateway);
        Assert.IsNotNull(config.InfluxDb);
    }

    [TestMethod]
    public void ConfigurationService_SaveConfiguration_ShouldWriteToFile()
    {
        // Arrange
        var config = _configurationService.GetConfiguration();
        config.EdgeGateway.ServerAddress = "updated-server";

        // Act
        _configurationService.SaveConfiguration(config);

        // Assert
        Assert.IsTrue(File.Exists(_testConfigPath));
        
        // 验证配置已更新
        var reloadedService = new ConfigurationService(_mockLogger.Object, _testConfigPath);
        var reloadedConfig = reloadedService.GetConfiguration();
        Assert.AreEqual("updated-server", reloadedConfig.EdgeGateway.ServerAddress);
        
        
    }

    private void CreateTestConfigFile()
    {
        var testConfig = @"{
  ""EdgeGateway"": {
    ""ServerAddress"": ""localhost"",
    ""MrpcPort"": 1883,
    ""Username"": ""test"",
    ""Password"": ""test"",
    ""PollingInterval"": 1000
  },
  ""InfluxDb"": {
    ""Url"": ""http://localhost:8086"",
    ""Token"": ""test-token"",
    ""Organization"": ""test-org"",
    ""Bucket"": ""test-bucket"",
    ""TimeoutSeconds"": 30,
    ""BatchSize"": 1000,
    ""FlushIntervalMs"": 1000
  },
  ""DataCollection"": {
    ""DevicePaths"": [
      ""test/device1"",
      ""test/device2""
    ],
    ""BatchSize"": 100,
    ""BatchInterval"": 5000
  }
}";

        File.WriteAllText(_testConfigPath, testConfig);
    }
}
