using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IOTPlatform.InfluxDbWrapper.Services;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using Serilog;

namespace IOTPlatform.SCADA.Tests;

[TestClass]
public class InfluxDbServiceTests
{
    private Mock<ILogger> _mockLogger;
    private InfluxDbConfiguration _testConfig;

    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger>();
        _testConfig = new InfluxDbConfiguration
        {
            Url = "http://localhost:8086",
            Token = "test-token",
            Organization = "test-org",
            Bucket = "test-bucket"
        };
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithValidData_ShouldBeValid()
    {
        // Act
        var isValid = _testConfig.IsValid();

        // Assert
        Assert.IsTrue(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyUrl_ShouldBeInvalid()
    {
        // Arrange
        _testConfig.Url = "";

        // Act
        var isValid = _testConfig.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyToken_ShouldBeInvalid()
    {
        // Arrange
        _testConfig.Token = "";

        // Act
        var isValid = _testConfig.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyOrganization_ShouldBeInvalid()
    {
        // Arrange
        _testConfig.Organization = "";

        // Act
        var isValid = _testConfig.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyBucket_ShouldBeInvalid()
    {
        // Arrange
        _testConfig.Bucket = "";

        // Act
        var isValid = _testConfig.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void IoTDataPoint_CreateMethod_ShouldCreateValidDataPoint()
    {
        // Arrange
        var deviceId = "device-001";
        var deviceName = "Temperature Sensor";
        var value = 25.5;
        var deviceType = "sensor";
        var location = "room-1";
        var unit = "°C";

        // Act
        var dataPoint = IoTDataPoint.Create(deviceId, deviceName, value, deviceType, location, unit);

        // Assert
        Assert.IsNotNull(dataPoint);
        Assert.AreEqual(deviceId, dataPoint.DeviceId);
        Assert.AreEqual(deviceName, dataPoint.DeviceName);
        Assert.AreEqual(value, dataPoint.Value);
        Assert.AreEqual(deviceType, dataPoint.DeviceType);
        Assert.AreEqual(location, dataPoint.Location);
        Assert.AreEqual(unit, dataPoint.Unit);
        Assert.AreEqual("Online", dataPoint.Status);
        Assert.AreEqual(100, dataPoint.Quality);
        Assert.IsTrue(dataPoint.Timestamp <= DateTime.UtcNow);
    }
}
