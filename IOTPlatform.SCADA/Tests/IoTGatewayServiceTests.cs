using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using IOTPlatform.InfluxDbWrapper.Services;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IOTPlatform.SCADA.Tests;

[TestClass]
public class IoTGatewayServiceTests
{
    private Mock<EdgeGatewayDataService> _mockDataService;
    private Mock<TimeSeriesDataService> _mockTimeSeriesService;
    private Mock<EdgeGatewayControlService> _mockControlService;
    private Mock<ConfigurationService> _mockConfigService;
    private Mock<ILogger> _mockLogger;
    private IoTGatewayService _gatewayService;

    [TestInitialize]
    public void Setup()
    {
        // 创建模拟对象
        _mockLogger = new Mock<ILogger>();
        
        var mockCommunicationService = new Mock<MrpcCommunicationService>(_mockLogger.Object);
        var mockInfluxDbService = new Mock<IInfluxDbService>();
        
        _mockDataService = new Mock<EdgeGatewayDataService>(mockCommunicationService.Object, _mockLogger.Object);
        _mockTimeSeriesService = new Mock<TimeSeriesDataService>(mockInfluxDbService.Object, _mockLogger.Object);
        _mockControlService = new Mock<EdgeGatewayControlService>(mockCommunicationService.Object, _mockLogger.Object);
        _mockConfigService = new Mock<ConfigurationService>(_mockLogger.Object, "test-config.json");

        // 创建被测试的服务
        _gatewayService = new IoTGatewayService(
            _mockDataService.Object,
            _mockTimeSeriesService.Object,
            _mockControlService.Object,
            _mockConfigService.Object,
            _mockLogger.Object);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _gatewayService?.Dispose();
    }

    [TestMethod]
    public void Constructor_WithValidParameters_ShouldCreateInstance()
    {
        // Assert
        Assert.IsNotNull(_gatewayService);
        Assert.IsFalse(_gatewayService.IsRunning);
    }

    [TestMethod]
    public async Task StartAsync_WithValidConfiguration_ShouldReturnTrue()
    {
        // Arrange
        var testConfig = CreateTestConfiguration();
        _mockConfigService.Setup(x => x.ValidateConfiguration()).Returns(true);
        _mockConfigService.Setup(x => x.GetConfiguration()).Returns(testConfig);
        _mockTimeSeriesService.Setup(x => x.TestConnectionAsync()).ReturnsAsync(true);
        _mockDataService.Setup(x => x.ConnectAsync(It.IsAny<EdgeGatewayConfiguration>())).ReturnsAsync(true);

        // Act
        var result = await _gatewayService.StartAsync();

        // Assert
        Assert.IsTrue(result);
        Assert.IsTrue(_gatewayService.IsRunning);
    }

    [TestMethod]
    public async Task StartAsync_WithInvalidConfiguration_ShouldReturnFalse()
    {
        // Arrange
        _mockConfigService.Setup(x => x.ValidateConfiguration()).Returns(false);

        // Act
        var result = await _gatewayService.StartAsync();

        // Assert
        Assert.IsFalse(result);
        Assert.IsFalse(_gatewayService.IsRunning);
    }

    [TestMethod]
    public async Task StartAsync_WithDatabaseConnectionFailure_ShouldReturnFalse()
    {
        // Arrange
        var testConfig = CreateTestConfiguration();
        _mockConfigService.Setup(x => x.ValidateConfiguration()).Returns(true);
        _mockConfigService.Setup(x => x.GetConfiguration()).Returns(testConfig);
        _mockTimeSeriesService.Setup(x => x.TestConnectionAsync()).ReturnsAsync(false);

        // Act
        var result = await _gatewayService.StartAsync();

        // Assert
        Assert.IsFalse(result);
        Assert.IsFalse(_gatewayService.IsRunning);
    }

    [TestMethod]
    public async Task StartAsync_WithGatewayConnectionFailure_ShouldReturnFalse()
    {
        // Arrange
        var testConfig = CreateTestConfiguration();
        _mockConfigService.Setup(x => x.ValidateConfiguration()).Returns(true);
        _mockConfigService.Setup(x => x.GetConfiguration()).Returns(testConfig);
        _mockTimeSeriesService.Setup(x => x.TestConnectionAsync()).ReturnsAsync(true);
        _mockDataService.Setup(x => x.ConnectAsync(It.IsAny<EdgeGatewayConfiguration>())).ReturnsAsync(false);

        // Act
        var result = await _gatewayService.StartAsync();

        // Assert
        Assert.IsFalse(result);
        Assert.IsFalse(_gatewayService.IsRunning);
    }

    [TestMethod]
    public async Task StopAsync_WhenRunning_ShouldStopService()
    {
        // Arrange
        await StartServiceSuccessfully();

        // Act
        await _gatewayService.StopAsync();

        // Assert
        Assert.IsFalse(_gatewayService.IsRunning);
        _mockDataService.Verify(x => x.StopPolling(), Times.Once());
        _mockDataService.Verify(x => x.Disconnect(), Times.Once());
    }

    [TestMethod]
    public async Task RestartAsync_ShouldStopAndStartService()
    {
        // Arrange
        await StartServiceSuccessfully();

        // Act
        var result = await _gatewayService.RestartAsync();

        // Assert
        Assert.IsTrue(result);
        Assert.IsTrue(_gatewayService.IsRunning);
    }

    [TestMethod]
    public void AddDevicePath_ShouldAddPathToDataService()
    {
        // Arrange
        var devicePath = "test/device/path";

        // Act
        _gatewayService.AddDevicePath(devicePath);

        // Assert
        _mockDataService.Verify(x => x.AddDevicePath(devicePath), Times.Once());
    }

    [TestMethod]
    public void RemoveDevicePath_ShouldRemovePathFromDataService()
    {
        // Arrange
        var devicePath = "test/device/path";

        // Act
        _gatewayService.RemoveDevicePath(devicePath);

        // Assert
        _mockDataService.Verify(x => x.RemoveDevicePath(devicePath), Times.Once());
    }

    [TestMethod]
    public async Task WriteDeviceDataAsync_WhenRunning_ShouldCallControlService()
    {
        // Arrange
        await StartServiceSuccessfully();
        var devicePath = "test/device";
        var value = 123.45;
        _mockControlService.Setup(x => x.WriteDeviceDataAsync(devicePath, value)).ReturnsAsync(true);

        // Act
        var result = await _gatewayService.WriteDeviceDataAsync(devicePath, value);

        // Assert
        Assert.IsTrue(result);
        _mockControlService.Verify(x => x.WriteDeviceDataAsync(devicePath, value), Times.Once());
    }

    [TestMethod]
    public async Task WriteDeviceDataAsync_WhenNotRunning_ShouldThrowException()
    {
        // Arrange
        var devicePath = "test/device";
        var value = 123.45;

        // Act & Assert
        await Assert.ThrowsExceptionAsync<InvalidOperationException>(
            () => _gatewayService.WriteDeviceDataAsync(devicePath, value));
    }

    [TestMethod]
    public async Task CallDeviceMethodAsync_WhenRunning_ShouldCallControlService()
    {
        // Arrange
        await StartServiceSuccessfully();
        var devicePath = "test/device";
        var methodName = "TestMethod";
        var parameters = new object[] { "param1", 123 };
        var expectedResult = "method result";
        _mockControlService.Setup(x => x.CallDeviceMethodAsync(devicePath, methodName, parameters))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _gatewayService.CallDeviceMethodAsync(devicePath, methodName, parameters);

        // Assert
        Assert.AreEqual(expectedResult, result);
        _mockControlService.Verify(x => x.CallDeviceMethodAsync(devicePath, methodName, parameters), Times.Once());
    }

    [TestMethod]
    public void GetStatistics_ShouldReturnValidStatistics()
    {
        // Act
        var stats = _gatewayService.GetStatistics();

        // Assert
        Assert.IsNotNull(stats);
        Assert.IsFalse(stats.IsRunning);
        Assert.AreEqual(0, stats.TotalDataPointsCollected);
        Assert.AreEqual(0, stats.TotalAlarmsReceived);
    }

    [TestMethod]
    public async Task QueryHistoryDataAsync_ShouldCallTimeSeriesService()
    {
        // Arrange
        var deviceId = "test-device";
        var start = DateTime.UtcNow.AddHours(-1);
        var end = DateTime.UtcNow;
        var expectedData = new List<IoTDataPoint>();
        _mockTimeSeriesService.Setup(x => x.QueryDeviceDataAsync(deviceId, start, end))
            .ReturnsAsync(expectedData);

        // Act
        var result = await _gatewayService.QueryHistoryDataAsync(deviceId, start, end);

        // Assert
        Assert.AreEqual(expectedData, result);
        _mockTimeSeriesService.Verify(x => x.QueryDeviceDataAsync(deviceId, start, end), Times.Once());
    }

    private async Task StartServiceSuccessfully()
    {
        var testConfig = CreateTestConfiguration();
        _mockConfigService.Setup(x => x.ValidateConfiguration()).Returns(true);
        _mockConfigService.Setup(x => x.GetConfiguration()).Returns(testConfig);
        _mockTimeSeriesService.Setup(x => x.TestConnectionAsync()).ReturnsAsync(true);
        _mockDataService.Setup(x => x.ConnectAsync(It.IsAny<EdgeGatewayConfiguration>())).ReturnsAsync(true);

        await _gatewayService.StartAsync();
    }

    private SystemConfiguration CreateTestConfiguration()
    {
        return new SystemConfiguration
        {
            EdgeGateway = new EdgeGatewayConfiguration
            {
                ServerAddress = "localhost",
                MrpcPort = 1883,
                Username = "test",
                Password = "test",
                PollingInterval = 1000
            },
            InfluxDb = new InfluxDbConfiguration
            {
                Url = "http://localhost:8086",
                Token = "test-token",
                Organization = "test-org",
                Bucket = "test-bucket"
            },
            DataCollection = new DataCollectionConfiguration
            {
                DevicePaths = new List<string> { "test/device1", "test/device2" },
                BatchSize = 100,
                BatchInterval = 5000
            }
        };
    }
}
