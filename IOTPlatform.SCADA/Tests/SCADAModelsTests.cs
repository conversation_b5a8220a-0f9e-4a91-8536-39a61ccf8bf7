using Microsoft.VisualStudio.TestTools.UnitTesting;
using IOTPlatform.SCADA.Models;
using System;

namespace IOTPlatform.SCADA.Tests;

[TestClass]
public class SCADAModelsTests
{
    [TestMethod]
    public void InfluxDbConfiguration_WithValidData_ShouldBeValid()
    {
        // Arrange
        var config = new InfluxDbConfiguration
        {
            Url = "http://localhost:8086",
            Token = "test-token",
            Organization = "test-org",
            Bucket = "test-bucket"
        };

        // Act
        var isValid = config.IsValid();

        // Assert
        Assert.IsTrue(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyUrl_ShouldBeInvalid()
    {
        // Arrange
        var config = new InfluxDbConfiguration
        {
            Url = "",
            Token = "test-token",
            Organization = "test-org",
            Bucket = "test-bucket"
        };

        // Act
        var isValid = config.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void InfluxDbConfiguration_WithEmptyToken_ShouldBeInvalid()
    {
        // Arrange
        var config = new InfluxDbConfiguration
        {
            Url = "http://localhost:8086",
            Token = "",
            Organization = "test-org",
            Bucket = "test-bucket"
        };

        // Act
        var isValid = config.IsValid();

        // Assert
        Assert.IsFalse(isValid);
    }

    [TestMethod]
    public void IoTDataPoint_CreateMethod_ShouldCreateValidDataPoint()
    {
        // Arrange
        var deviceId = "device-001";
        var deviceName = "Temperature Sensor";
        var value = 25.5;
        var deviceType = "sensor";
        var location = "room-1";
        var unit = "°C";

        // Act
        var dataPoint = IoTDataPoint.Create(deviceId, deviceName, value, deviceType, location, unit);

        // Assert
        Assert.IsNotNull(dataPoint);
        Assert.AreEqual(deviceId, dataPoint.DeviceId);
        Assert.AreEqual(deviceName, dataPoint.DeviceName);
        Assert.AreEqual(value, dataPoint.Value);
        Assert.AreEqual(deviceType, dataPoint.DeviceType);
        Assert.AreEqual(location, dataPoint.Location);
        Assert.AreEqual(unit, dataPoint.Unit);
        Assert.AreEqual("Online", dataPoint.Status);
        Assert.AreEqual(100, dataPoint.Quality);
        Assert.IsTrue(dataPoint.Timestamp <= DateTime.UtcNow);
    }

    [TestMethod]
    public void IoTDataPoint_DefaultConstructor_ShouldSetDefaultValues()
    {
        // Act
        var dataPoint = new IoTDataPoint();

        // Assert
        Assert.AreEqual(string.Empty, dataPoint.DeviceId);
        Assert.AreEqual(string.Empty, dataPoint.DeviceName);
        Assert.AreEqual(0.0, dataPoint.Value);
        Assert.AreEqual(100, dataPoint.Quality);
        Assert.AreEqual("Online", dataPoint.Status);
        Assert.IsTrue(dataPoint.Timestamp <= DateTime.UtcNow);
    }

    [TestMethod]
    public void AlarmInfo_Properties_ShouldSetAndGetCorrectly()
    {
        // Arrange
        var alarmInfo = new AlarmInfo();
        var testId = "alarm-001";
        var testDeviceId = "device-001";
        var testMessage = "Temperature too high";
        var testLevel = AlarmLevel.Warning;
        var testTimestamp = DateTime.UtcNow;

        // Act
        alarmInfo.Id = testId;
        alarmInfo.DeviceId = testDeviceId;
        alarmInfo.Message = testMessage;
        alarmInfo.Level = testLevel;
        alarmInfo.Timestamp = testTimestamp;

        // Assert
        Assert.AreEqual(testId, alarmInfo.Id);
        Assert.AreEqual(testDeviceId, alarmInfo.DeviceId);
        Assert.AreEqual(testMessage, alarmInfo.Message);
        Assert.AreEqual(testLevel, alarmInfo.Level);
        Assert.AreEqual(testTimestamp, alarmInfo.Timestamp);
        Assert.IsFalse(alarmInfo.IsAcknowledged);
    }

    [TestMethod]
    public void AlarmInfo_AcknowledgeAlarm_ShouldSetAcknowledgedProperties()
    {
        // Arrange
        var alarmInfo = new AlarmInfo
        {
            Id = "alarm-001",
            DeviceId = "device-001",
            Message = "Test alarm",
            Level = AlarmLevel.Warning,
            Timestamp = DateTime.UtcNow
        };

        var acknowledgedBy = "admin";
        var acknowledgedTime = DateTime.UtcNow;

        // Act
        alarmInfo.IsAcknowledged = true;
        alarmInfo.AcknowledgedBy = acknowledgedBy;
        alarmInfo.AcknowledgedTime = acknowledgedTime;

        // Assert
        Assert.IsTrue(alarmInfo.IsAcknowledged);
        Assert.AreEqual(acknowledgedBy, alarmInfo.AcknowledgedBy);
        Assert.AreEqual(acknowledgedTime, alarmInfo.AcknowledgedTime);
    }

    [TestMethod]
    public void AlarmStatistics_Properties_ShouldSetAndGetCorrectly()
    {
        // Arrange
        var stats = new AlarmStatistics();

        // Act
        stats.TotalCount = 10;
        stats.InfoCount = 3;
        stats.WarningCount = 4;
        stats.CriticalCount = 2;
        stats.EmergencyCount = 1;

        // Assert
        Assert.AreEqual(10, stats.TotalCount);
        Assert.AreEqual(3, stats.InfoCount);
        Assert.AreEqual(4, stats.WarningCount);
        Assert.AreEqual(2, stats.CriticalCount);
        Assert.AreEqual(1, stats.EmergencyCount);
    }

    [TestMethod]
    public void AlarmLevel_EnumValues_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.AreEqual(0, (int)AlarmLevel.Info);
        Assert.AreEqual(1, (int)AlarmLevel.Warning);
        Assert.AreEqual(2, (int)AlarmLevel.Critical);
        Assert.AreEqual(3, (int)AlarmLevel.Emergency);
    }

    [TestMethod]
    public void DeviceInfo_Properties_ShouldSetAndGetCorrectly()
    {
        // Arrange
        var deviceInfo = new DeviceInfo();
        var testName = "Temperature Sensor";
        var testPath = "building1/room1/temp";
        var testValue = 25.5;
        var testStatus = "Online";
        var testTime = DateTime.UtcNow;

        // Act
        deviceInfo.Name = testName;
        deviceInfo.Path = testPath;
        deviceInfo.IsOnline = true;
        deviceInfo.Value = testValue;
        deviceInfo.Status = testStatus;
        deviceInfo.LastUpdateTime = testTime;

        // Assert
        Assert.AreEqual(testName, deviceInfo.Name);
        Assert.AreEqual(testPath, deviceInfo.Path);
        Assert.IsTrue(deviceInfo.IsOnline);
        Assert.AreEqual(testValue, deviceInfo.Value);
        Assert.AreEqual(testStatus, deviceInfo.Status);
        Assert.AreEqual(testTime, deviceInfo.LastUpdateTime);
    }

    [TestMethod]
    public void DeviceDataRecord_Properties_ShouldSetAndGetCorrectly()
    {
        // Arrange
        var record = new DeviceDataRecord();
        var testDeviceId = "device-001";
        var testDeviceName = "Test Device";
        var testValue = 42.0;
        var testTimestamp = DateTime.UtcNow;

        // Act
        record.DeviceId = testDeviceId;
        record.DeviceName = testDeviceName;
        record.Value = testValue;
        record.Timestamp = testTimestamp;

        // Assert
        Assert.AreEqual(testDeviceId, record.DeviceId);
        Assert.AreEqual(testDeviceName, record.DeviceName);
        Assert.AreEqual(testValue, record.Value);
        Assert.AreEqual(testTimestamp, record.Timestamp);
    }
}
