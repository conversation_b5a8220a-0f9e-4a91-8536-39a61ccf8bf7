using System.Collections.ObjectModel;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using Serilog;

namespace IOTPlatform.SCADA.ViewModels;

/// <summary>
/// 报警管理ViewModel
/// </summary>
public class AlarmManagementViewModel : BindableBase, IDisposable
{
    private readonly AlarmManager _alarmManager;
    private readonly AlarmNotificationService _notificationService;
    private readonly ILogger _logger;
    private readonly Timer _refreshTimer;

    #region 属性

    private ObservableCollection<AlarmInstance> _activeAlarms = new();
    public ObservableCollection<AlarmInstance> ActiveAlarms
    {
        get => _activeAlarms;
        set => SetProperty(ref _activeAlarms, value);
    }

    private ObservableCollection<AlarmRule> _alarmRules = new();
    public ObservableCollection<AlarmRule> AlarmRules
    {
        get => _alarmRules;
        set => SetProperty(ref _alarmRules, value);
    }

    private AlarmInstance? _selectedAlarm;
    public AlarmInstance? SelectedAlarm
    {
        get => _selectedAlarm;
        set => SetProperty(ref _selectedAlarm, value);
    }

    private AlarmRule? _selectedRule;
    public AlarmRule? SelectedRule
    {
        get => _selectedRule;
        set => SetProperty(ref _selectedRule, value);
    }

    private AlarmStatistics _statistics = new();
    public AlarmStatistics Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    private string _filterText = "";
    public string FilterText
    {
        get => _filterText;
        set
        {
            SetProperty(ref _filterText, value);
            ApplyFilter();
        }
    }

    private AlarmLevel? _filterLevel;
    public AlarmLevel? FilterLevel
    {
        get => _filterLevel;
        set
        {
            SetProperty(ref _filterLevel, value);
            ApplyFilter();
        }
    }

    private AlarmStatus? _filterStatus;
    public AlarmStatus? FilterStatus
    {
        get => _filterStatus;
        set
        {
            SetProperty(ref _filterStatus, value);
            ApplyFilter();
        }
    }

    private bool _soundEnabled = true;
    public bool SoundEnabled
    {
        get => _soundEnabled;
        set
        {
            SetProperty(ref _soundEnabled, value);
            _notificationService.SetSoundEnabled(value);
        }
    }

    private string _acknowledgmentComment = "";
    public string AcknowledgmentComment
    {
        get => _acknowledgmentComment;
        set => SetProperty(ref _acknowledgmentComment, value);
    }

    #endregion

    #region 命令

    public ICommand RefreshCommand { get; }
    public ICommand AcknowledgeAlarmCommand { get; }
    public ICommand SuppressAlarmCommand { get; }
    public ICommand AddRuleCommand { get; }
    public ICommand EditRuleCommand { get; }
    public ICommand DeleteRuleCommand { get; }
    public ICommand ClearFilterCommand { get; }
    public ICommand ExportAlarmsCommand { get; }

    #endregion

    public AlarmManagementViewModel(
        AlarmManager alarmManager,
        AlarmNotificationService notificationService,
        ILogger logger)
    {
        _alarmManager = alarmManager ?? throw new ArgumentNullException(nameof(alarmManager));
        _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 初始化命令
        RefreshCommand = new DelegateCommand(RefreshData);
        AcknowledgeAlarmCommand = new DelegateCommand(AcknowledgeAlarm, CanAcknowledgeAlarm);
        SuppressAlarmCommand = new DelegateCommand(SuppressAlarm, CanSuppressAlarm);
        AddRuleCommand = new DelegateCommand(AddRule);
        EditRuleCommand = new DelegateCommand(EditRule, CanEditRule);
        DeleteRuleCommand = new DelegateCommand(DeleteRule, CanDeleteRule);
        ClearFilterCommand = new DelegateCommand(ClearFilter);
        ExportAlarmsCommand = new DelegateCommand(async () => await ExportAlarmsAsync());

        // 订阅报警事件
        _alarmManager.AlarmTriggered += OnAlarmTriggered;
        _alarmManager.AlarmAcknowledged += OnAlarmAcknowledged;
        _alarmManager.AlarmRecovered += OnAlarmRecovered;
        _alarmManager.AlarmSuppressed += OnAlarmSuppressed;

        // 启动定时刷新
        _refreshTimer = new Timer(AutoRefresh, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));

        // 初始化默认报警规则
        InitializeDefaultRules();
        
        // 加载初始数据
        RefreshData();

        _logger.Information("报警管理ViewModel已初始化");
    }

    /// <summary>
    /// 初始化默认报警规则
    /// </summary>
    private void InitializeDefaultRules()
    {
        var defaultRules = new[]
        {
            new AlarmRule
            {
                Name = "温度过高报警",
                Description = "温度超过80°C时触发报警",
                DevicePath = "device/temperature",
                DeviceName = "温度传感器",
                AlarmType = AlarmType.Value,
                Level = AlarmLevel.Critical,
                Condition = AlarmCondition.GreaterThan,
                Threshold = 80.0,
                DelayTime = TimeSpan.FromSeconds(5),
                MessageTemplate = "{DeviceName} 温度过高: {CurrentValue}°C (阈值: {Threshold}°C)",
                NotificationMethods = NotificationMethod.All
            },
            new AlarmRule
            {
                Name = "温度过低报警",
                Description = "温度低于0°C时触发报警",
                DevicePath = "device/temperature",
                DeviceName = "温度传感器",
                AlarmType = AlarmType.Value,
                Level = AlarmLevel.Warning,
                Condition = AlarmCondition.LessThan,
                Threshold = 0.0,
                DelayTime = TimeSpan.FromSeconds(5),
                MessageTemplate = "{DeviceName} 温度过低: {CurrentValue}°C (阈值: {Threshold}°C)",
                NotificationMethods = NotificationMethod.UI | NotificationMethod.Sound | NotificationMethod.Log
            },
            new AlarmRule
            {
                Name = "压力异常报警",
                Description = "压力超出正常范围时触发报警",
                DevicePath = "device/pressure",
                DeviceName = "压力传感器",
                AlarmType = AlarmType.Value,
                Level = AlarmLevel.Critical,
                Condition = AlarmCondition.OutOfRange,
                LowThreshold = 0.1,
                HighThreshold = 2.0,
                DelayTime = TimeSpan.FromSeconds(3),
                MessageTemplate = "{DeviceName} 压力异常: {CurrentValue}MPa (正常范围: {LowThreshold}-{HighThreshold}MPa)",
                NotificationMethods = NotificationMethod.All
            },
            new AlarmRule
            {
                Name = "液位过低报警",
                Description = "液位低于20%时触发报警",
                DevicePath = "device/level",
                DeviceName = "液位计",
                AlarmType = AlarmType.Value,
                Level = AlarmLevel.Critical,
                Condition = AlarmCondition.LessThan,
                Threshold = 20.0,
                DelayTime = TimeSpan.FromSeconds(10),
                MessageTemplate = "{DeviceName} 液位过低: {CurrentValue}% (阈值: {Threshold}%)",
                NotificationMethods = NotificationMethod.UI | NotificationMethod.Sound | NotificationMethod.Log
            }
        };

        foreach (var rule in defaultRules)
        {
            _alarmManager.AddAlarmRule(rule);
        }
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    public void RefreshData()
    {
        try
        {
            // 刷新活动报警
            var activeAlarms = _alarmManager.GetActiveAlarms().ToList();
            ActiveAlarms.Clear();
            foreach (var alarm in activeAlarms.OrderByDescending(a => a.TriggeredTime))
            {
                ActiveAlarms.Add(alarm);
            }

            // 刷新报警规则
            var rules = _alarmManager.GetAlarmRules().ToList();
            AlarmRules.Clear();
            foreach (var rule in rules.OrderBy(r => r.Name))
            {
                AlarmRules.Add(rule);
            }

            // 更新统计信息
            Statistics = _alarmManager.GetAlarmStatistics(TimeSpan.FromDays(1));

            // 更新命令状态
            RaiseCanExecuteChanged();

            _logger.Debug("报警数据已刷新: 活动报警 {ActiveCount}, 规则 {RuleCount}", 
                ActiveAlarms.Count, AlarmRules.Count);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "刷新报警数据失败");
        }
    }

    /// <summary>
    /// 确认报警
    /// </summary>
    public void AcknowledgeAlarm()
    {
        if (SelectedAlarm != null)
        {
            var success = _alarmManager.AcknowledgeAlarm(
                SelectedAlarm.Id, 
                "操作员", 
                AcknowledgmentComment);

            if (success)
            {
                AcknowledgmentComment = "";
                RefreshData();
                _logger.Information("报警已确认: {AlarmId}", SelectedAlarm.Id);
            }
        }
    }

    /// <summary>
    /// 抑制报警
    /// </summary>
    public void SuppressAlarm()
    {
        if (SelectedAlarm != null)
        {
            var success = _alarmManager.SuppressAlarm(SelectedAlarm.Id, "操作员");
            if (success)
            {
                RefreshData();
                _logger.Information("报警已抑制: {AlarmId}", SelectedAlarm.Id);
            }
        }
    }

    /// <summary>
    /// 添加规则
    /// </summary>
    public void AddRule()
    {
        var newRule = new AlarmRule
        {
            Name = "新报警规则",
            DevicePath = "device/new",
            DeviceName = "新设备",
            Level = AlarmLevel.Warning,
            Condition = AlarmCondition.GreaterThan,
            Threshold = 100.0
        };

        _alarmManager.AddAlarmRule(newRule);
        RefreshData();
        _logger.Information("添加新报警规则: {RuleName}", newRule.Name);
    }

    /// <summary>
    /// 编辑规则
    /// </summary>
    public void EditRule()
    {
        if (SelectedRule != null)
        {
            // 这里应该打开编辑对话框
            // 为了演示，我们只是记录日志
            _logger.Information("编辑报警规则: {RuleName}", SelectedRule.Name);
        }
    }

    /// <summary>
    /// 删除规则
    /// </summary>
    public void DeleteRule()
    {
        if (SelectedRule != null)
        {
            var success = _alarmManager.RemoveAlarmRule(SelectedRule.Id);
            if (success)
            {
                RefreshData();
                _logger.Information("删除报警规则: {RuleName}", SelectedRule.Name);
            }
        }
    }

    /// <summary>
    /// 清除过滤器
    /// </summary>
    public void ClearFilter()
    {
        FilterText = "";
        FilterLevel = null;
        FilterStatus = null;
    }

    /// <summary>
    /// 导出报警数据
    /// </summary>
    public async Task ExportAlarmsAsync()
    {
        try
        {
            // 这里应该实现实际的导出逻辑
            await Task.Delay(1000); // 模拟导出过程
            _logger.Information("报警数据导出完成");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "导出报警数据失败");
        }
    }

    private void ApplyFilter()
    {
        // 这里应该实现过滤逻辑
        // 为了简化，我们重新加载数据
        RefreshData();
    }

    private bool CanAcknowledgeAlarm()
    {
        return SelectedAlarm != null && SelectedAlarm.Status == AlarmStatus.Active;
    }

    private bool CanSuppressAlarm()
    {
        return SelectedAlarm != null && SelectedAlarm.Status != AlarmStatus.Suppressed;
    }

    private bool CanEditRule()
    {
        return SelectedRule != null;
    }

    private bool CanDeleteRule()
    {
        return SelectedRule != null;
    }

    private void RaiseCanExecuteChanged()
    {
        (AcknowledgeAlarmCommand as DelegateCommand)?.RaiseCanExecuteChanged();
        (SuppressAlarmCommand as DelegateCommand)?.RaiseCanExecuteChanged();
        (EditRuleCommand as DelegateCommand)?.RaiseCanExecuteChanged();
        (DeleteRuleCommand as DelegateCommand)?.RaiseCanExecuteChanged();
    }

    private void AutoRefresh(object? state)
    {
        try
        {
            RefreshData();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "自动刷新报警数据失败");
        }
    }

    #region 事件处理

    private void OnAlarmTriggered(object? sender, AlarmEventArgs e)
    {
        // 发送通知
        _ = Task.Run(async () => await _notificationService.SendNotificationAsync(e.Alarm, e.Rule));
        
        // 刷新UI
        RefreshData();
    }

    private void OnAlarmAcknowledged(object? sender, AlarmEventArgs e)
    {
        RefreshData();
    }

    private void OnAlarmRecovered(object? sender, AlarmEventArgs e)
    {
        RefreshData();
    }

    private void OnAlarmSuppressed(object? sender, AlarmEventArgs e)
    {
        RefreshData();
    }

    #endregion

    public void Dispose()
    {
        _refreshTimer?.Dispose();
        
        // 取消事件订阅
        _alarmManager.AlarmTriggered -= OnAlarmTriggered;
        _alarmManager.AlarmAcknowledged -= OnAlarmAcknowledged;
        _alarmManager.AlarmRecovered -= OnAlarmRecovered;
        _alarmManager.AlarmSuppressed -= OnAlarmSuppressed;
        
        _logger.Information("报警管理ViewModel已释放");
    }
}
