using System.Collections.ObjectModel;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using IOTPlatform.SCADA.Models;
using IOTPlatform.SCADA.Services;
using IOTControls;
using Avalonia.Media;
using Serilog;

namespace IOTPlatform.SCADA.ViewModels;

/// <summary>
/// 趋势图ViewModel
/// </summary>
public class TrendChartViewModel : BindableBase, IDisposable
{
    private readonly TimeSeriesDataService _timeSeriesService;
    private readonly ILogger _logger;
    private readonly Timer _refreshTimer;
    private readonly object _lockObject = new();

    #region 属性

    private TrendChartConfig _config = new();
    public TrendChartConfig Config
    {
        get => _config;
        set => SetProperty(ref _config, value);
    }

    private ObservableCollection<TrendDataSeries> _dataSeries = new();
    public ObservableCollection<TrendDataSeries> DataSeries
    {
        get => _dataSeries;
        set => SetProperty(ref _dataSeries, value);
    }

    private ObservableCollection<IOTCurveItem> _curveItems = new();
    public ObservableCollection<IOTCurveItem> CurveItems
    {
        get => _curveItems;
        set => SetProperty(ref _curveItems, value);
    }

    private bool _isLoading;
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    private string _statusMessage = "就绪";
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    private DateTime _selectedStartTime = DateTime.Now.AddHours(-24);
    public DateTime SelectedStartTime
    {
        get => _selectedStartTime;
        set => SetProperty(ref _selectedStartTime, value);
    }

    private DateTime _selectedEndTime = DateTime.Now;
    public DateTime SelectedEndTime
    {
        get => _selectedEndTime;
        set => SetProperty(ref _selectedEndTime, value);
    }

    private TrendChartStatistics _statistics = new();
    public TrendChartStatistics Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    #endregion

    #region 命令

    public ICommand LoadDataCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand ExportDataCommand { get; }
    public ICommand AddSeriesCommand { get; }
    public ICommand RemoveSeriesCommand { get; }
    public ICommand ClearDataCommand { get; }
    public ICommand ZoomInCommand { get; }
    public ICommand ZoomOutCommand { get; }
    public ICommand ResetZoomCommand { get; }

    #endregion

    public TrendChartViewModel(TimeSeriesDataService timeSeriesService, ILogger logger)
    {
        _timeSeriesService = timeSeriesService ?? throw new ArgumentNullException(nameof(timeSeriesService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 初始化命令
        LoadDataCommand = new DelegateCommand(async () => await LoadDataAsync());
        RefreshCommand = new DelegateCommand(async () => await RefreshDataAsync());
        ExportDataCommand = new DelegateCommand(async () => await ExportDataAsync());
        AddSeriesCommand = new DelegateCommand<string>(AddSeries);
        RemoveSeriesCommand = new DelegateCommand<TrendDataSeries>(RemoveSeries);
        ClearDataCommand = new DelegateCommand(ClearData);
        ZoomInCommand = new DelegateCommand(ZoomIn);
        ZoomOutCommand = new DelegateCommand(ZoomOut);
        ResetZoomCommand = new DelegateCommand(ResetZoom);

        // 启动自动刷新定时器
        _refreshTimer = new Timer(AutoRefresh, null, Config.RefreshInterval, Config.RefreshInterval);

        InitializeDefaultSeries();
        
        _logger.Information("趋势图ViewModel已初始化");
    }

    /// <summary>
    /// 初始化默认数据系列
    /// </summary>
    private void InitializeDefaultSeries()
    {
        var defaultSeries = new[]
        {
            new TrendDataSeries
            {
                Name = "温度",
                DevicePath = "device/temperature",
                Unit = "°C",
                LineColor = Colors.Red,
                LineThickness = 2.0f
            },
            new TrendDataSeries
            {
                Name = "压力",
                DevicePath = "device/pressure",
                Unit = "MPa",
                LineColor = Colors.Blue,
                LineThickness = 2.0f
            },
            new TrendDataSeries
            {
                Name = "流量",
                DevicePath = "device/flowrate",
                Unit = "L/min",
                LineColor = Colors.Green,
                LineThickness = 2.0f
            },
            new TrendDataSeries
            {
                Name = "液位",
                DevicePath = "device/level",
                Unit = "%",
                LineColor = Colors.Orange,
                LineThickness = 2.0f
            }
        };

        foreach (var series in defaultSeries)
        {
            DataSeries.Add(series);
        }
    }

    /// <summary>
    /// 加载历史数据
    /// </summary>
    public async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载历史数据...";

            var query = new TrendDataQuery
            {
                DevicePaths = DataSeries.Where(s => s.IsVisible).Select(s => s.DevicePath).ToList(),
                StartTime = SelectedStartTime,
                EndTime = SelectedEndTime,
                MaxPoints = Config.MaxDataPoints
            };

            await LoadDataForQuery(query);

            UpdateCurveItems();
            UpdateStatistics();

            StatusMessage = $"已加载 {DataSeries.Sum(s => s.DataPointCount)} 个数据点";
            _logger.Information("历史数据加载完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载数据失败: {ex.Message}";
            _logger.Error(ex, "加载历史数据失败");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    public async Task RefreshDataAsync()
    {
        await LoadDataAsync();
    }

    /// <summary>
    /// 添加数据系列
    /// </summary>
    public void AddSeries(string? devicePath)
    {
        if (string.IsNullOrEmpty(devicePath)) return;

        var series = new TrendDataSeries
        {
            Name = $"设备 {DataSeries.Count + 1}",
            DevicePath = devicePath,
            LineColor = GetNextColor(),
            LineThickness = 2.0f
        };

        DataSeries.Add(series);
        _logger.Information("添加数据系列: {DevicePath}", devicePath);
    }

    /// <summary>
    /// 移除数据系列
    /// </summary>
    public void RemoveSeries(TrendDataSeries? series)
    {
        if (series != null && DataSeries.Contains(series))
        {
            DataSeries.Remove(series);
            UpdateCurveItems();
            _logger.Information("移除数据系列: {SeriesName}", series.Name);
        }
    }

    /// <summary>
    /// 清除所有数据
    /// </summary>
    public void ClearData()
    {
        foreach (var series in DataSeries)
        {
            series.ClearDataPoints();
        }
        UpdateCurveItems();
        UpdateStatistics();
        StatusMessage = "数据已清除";
    }

    /// <summary>
    /// 放大
    /// </summary>
    public void ZoomIn()
    {
        var timeSpan = SelectedEndTime - SelectedStartTime;
        var newTimeSpan = TimeSpan.FromTicks(timeSpan.Ticks / 2);
        var center = SelectedStartTime.Add(TimeSpan.FromTicks(timeSpan.Ticks / 2));
        
        SelectedStartTime = center.Subtract(TimeSpan.FromTicks(newTimeSpan.Ticks / 2));
        SelectedEndTime = center.Add(TimeSpan.FromTicks(newTimeSpan.Ticks / 2));
        
        _ = Task.Run(async () => await LoadDataAsync());
    }

    /// <summary>
    /// 缩小
    /// </summary>
    public void ZoomOut()
    {
        var timeSpan = SelectedEndTime - SelectedStartTime;
        var newTimeSpan = TimeSpan.FromTicks(timeSpan.Ticks * 2);
        var center = SelectedStartTime.Add(TimeSpan.FromTicks(timeSpan.Ticks / 2));
        
        SelectedStartTime = center.Subtract(TimeSpan.FromTicks(newTimeSpan.Ticks / 2));
        SelectedEndTime = center.Add(TimeSpan.FromTicks(newTimeSpan.Ticks / 2));
        
        _ = Task.Run(async () => await LoadDataAsync());
    }

    /// <summary>
    /// 重置缩放
    /// </summary>
    public void ResetZoom()
    {
        SelectedStartTime = DateTime.Now.AddHours(-24);
        SelectedEndTime = DateTime.Now;
        _ = Task.Run(async () => await LoadDataAsync());
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    public async Task ExportDataAsync()
    {
        try
        {
            StatusMessage = "正在导出数据...";
            
            // 这里应该实现实际的数据导出逻辑
            // 例如导出为CSV、Excel等格式
            await Task.Delay(1000); // 模拟导出过程
            
            StatusMessage = "数据导出完成";
            _logger.Information("趋势图数据导出完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"导出失败: {ex.Message}";
            _logger.Error(ex, "导出趋势图数据失败");
        }
    }

    private async Task LoadDataForQuery(TrendDataQuery query)
    {
        var tasks = new List<Task>();

        foreach (var series in DataSeries.Where(s => s.IsVisible))
        {
            tasks.Add(LoadSeriesData(series, query.StartTime, query.EndTime, query.MaxPoints));
        }

        await Task.WhenAll(tasks);
    }

    private async Task LoadSeriesData(TrendDataSeries series, DateTime startTime, DateTime endTime, int? maxPoints)
    {
        try
        {
            var records = await _timeSeriesService.GetHistoricalDataAsync(series.DevicePath, startTime, endTime);
            
            series.ClearDataPoints();
            
            // 如果数据点太多，进行采样
            if (maxPoints.HasValue && records.Count() > maxPoints.Value)
            {
                records = SampleData(records, maxPoints.Value);
            }

            foreach (var record in records.OrderBy(r => r.Timestamp))
            {
                series.AddDataPoint(record.Timestamp, record.Value);
            }

            _logger.Debug("加载数据系列: {SeriesName}, 数据点数: {Count}", series.Name, series.DataPointCount);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "加载数据系列失败: {SeriesName}", series.Name);
        }
    }

    private IEnumerable<DeviceDataRecord> SampleData(IEnumerable<DeviceDataRecord> records, int maxPoints)
    {
        var recordList = records.ToList();
        if (recordList.Count <= maxPoints) return recordList;

        var step = (double)recordList.Count / maxPoints;
        var sampledRecords = new List<DeviceDataRecord>();

        for (int i = 0; i < maxPoints; i++)
        {
            var index = (int)(i * step);
            if (index < recordList.Count)
            {
                sampledRecords.Add(recordList[index]);
            }
        }

        return sampledRecords;
    }

    private void UpdateCurveItems()
    {
        lock (_lockObject)
        {
            CurveItems.Clear();
            
            foreach (var series in DataSeries.Where(s => s.IsVisible && s.DataPointCount > 0))
            {
                var curveItem = series.ToIOTCurveItem();
                CurveItems.Add(curveItem);
            }
        }
    }

    private void UpdateStatistics()
    {
        var stats = new TrendChartStatistics
        {
            TotalSeries = DataSeries.Count,
            TotalDataPoints = DataSeries.Sum(s => s.DataPointCount)
        };

        if (stats.TotalDataPoints > 0)
        {
            var allDataPoints = DataSeries.SelectMany(s => s.DataPoints).ToList();
            stats.DataStartTime = allDataPoints.Min(p => p.Timestamp);
            stats.DataEndTime = allDataPoints.Max(p => p.Timestamp);

            foreach (var series in DataSeries)
            {
                if (series.DataPointCount > 0)
                {
                    stats.SeriesStats[series.Name] = new SeriesStatistics
                    {
                        SeriesName = series.Name,
                        DataPointCount = series.DataPointCount,
                        MinValue = series.MinValue,
                        MaxValue = series.MaxValue,
                        AverageValue = series.AverageValue,
                        FirstDataTime = series.DataPoints.Min(p => p.Timestamp),
                        LastDataTime = series.DataPoints.Max(p => p.Timestamp)
                    };
                }
            }
        }

        Statistics = stats;
    }

    private Color GetNextColor()
    {
        var colors = new[] { Colors.Red, Colors.Blue, Colors.Green, Colors.Orange, Colors.Purple, Colors.Brown, Colors.Pink, Colors.Gray };
        return colors[DataSeries.Count % colors.Length];
    }

    private void AutoRefresh(object? state)
    {
        if (!IsLoading)
        {
            _ = Task.Run(async () => await RefreshDataAsync());
        }
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
        _logger.Information("趋势图ViewModel已释放");
    }
}
