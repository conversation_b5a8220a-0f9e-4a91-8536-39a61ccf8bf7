<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:IOTPlatform.SCADA.ViewModels"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="IOTPlatform.SCADA.Views.AlarmManagementView"
             x:DataType="vm:AlarmManagementViewModel">

  <Design.DataContext>
    <vm:AlarmManagementViewModel/>
  </Design.DataContext>

  <Grid>
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
      <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>

    <!-- 工具栏 -->
    <Border Grid.Row="0" Background="#2C3E50" Padding="10">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧标题和过滤器 -->
        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
          <TextBlock Text="报警管理系统" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,20,0"/>
          
          <TextBox Watermark="搜索报警..." Text="{Binding FilterText}" Width="200" Margin="0,0,10,0"/>
          
          <ComboBox PlaceholderText="报警级别" SelectedItem="{Binding FilterLevel}" Width="120" Margin="0,0,10,0">
            <ComboBox.Items>
              <x:Null/>
              <x:Static Member="vm:AlarmLevel.Critical"/>
              <x:Static Member="vm:AlarmLevel.High"/>
              <x:Static Member="vm:AlarmLevel.Medium"/>
              <x:Static Member="vm:AlarmLevel.Low"/>
            </ComboBox.Items>
          </ComboBox>
          
          <ComboBox PlaceholderText="报警状态" SelectedItem="{Binding FilterStatus}" Width="120" Margin="0,0,10,0">
            <ComboBox.Items>
              <x:Null/>
              <x:Static Member="vm:AlarmStatus.Active"/>
              <x:Static Member="vm:AlarmStatus.Acknowledged"/>
              <x:Static Member="vm:AlarmStatus.Recovered"/>
              <x:Static Member="vm:AlarmStatus.Suppressed"/>
            </ComboBox.Items>
          </ComboBox>
        </StackPanel>

        <!-- 右侧操作按钮 -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
          <CheckBox Content="声音提醒" IsChecked="{Binding SoundEnabled}" Foreground="White"/>
          <Button Content="刷新" Command="{Binding RefreshCommand}" 
                  Background="#3498DB" Foreground="White" Padding="15,5"/>
          <Button Content="清除过滤" Command="{Binding ClearFilterCommand}" 
                  Background="#95A5A6" Foreground="White" Padding="15,5"/>
          <Button Content="导出" Command="{Binding ExportAlarmsCommand}" 
                  Background="#34495E" Foreground="White" Padding="15,5"/>
        </StackPanel>
      </Grid>
    </Border>

    <!-- 主要内容区域 -->
    <TabControl Grid.Row="1" Margin="10">
      
      <!-- 活动报警标签页 -->
      <TabItem Header="活动报警">
        <Grid>
          <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
          </Grid.ColumnDefinitions>

          <!-- 报警列表 -->
          <Border Grid.Column="0" Background="White" CornerRadius="5" Margin="0,0,5,0"
                  BorderBrush="#BDC3C7" BorderThickness="1">
            <Grid Margin="10">
              <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
              </Grid.RowDefinitions>

              <TextBlock Grid.Row="0" Text="活动报警列表" FontSize="16" FontWeight="Bold" 
                         Foreground="#2C3E50" Margin="0,0,0,10"/>

              <DataGrid Grid.Row="1" ItemsSource="{Binding ActiveAlarms}" 
                        SelectedItem="{Binding SelectedAlarm}"
                        AutoGenerateColumns="False" IsReadOnly="True"
                        GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                <DataGrid.Columns>
                  <DataGridTextColumn Header="级别" Binding="{Binding Level}" Width="60"/>
                  <DataGridTextColumn Header="设备" Binding="{Binding DeviceName}" Width="120"/>
                  <DataGridTextColumn Header="消息" Binding="{Binding Message}" Width="*"/>
                  <DataGridTextColumn Header="当前值" Binding="{Binding CurrentValue, StringFormat=F2}" Width="80"/>
                  <DataGridTextColumn Header="阈值" Binding="{Binding Threshold, StringFormat=F2}" Width="80"/>
                  <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                  <DataGridTextColumn Header="触发时间" Binding="{Binding TriggeredTime, StringFormat=MM-dd HH:mm:ss}" Width="120"/>
                  <DataGridTextColumn Header="持续时间" Binding="{Binding Duration}" Width="100"/>
                </DataGrid.Columns>
                
                <!-- 行样式 -->
                <DataGrid.Styles>
                  <Style Selector="DataGridRow">
                    <Style Selector="^[IsSelected=False]">
                      <Style Selector="^ /template/ Border#BackgroundBorder">
                        <Setter Property="Background">
                          <Setter.Value>
                            <MultiBinding Converter="{x:Static vm:AlarmLevelToColorConverter.Instance}">
                              <Binding Path="Level"/>
                            </MultiBinding>
                          </Setter.Value>
                        </Setter>
                      </Style>
                    </Style>
                  </Style>
                </DataGrid.Styles>
              </DataGrid>
            </Grid>
          </Border>

          <!-- 报警详情和操作 -->
          <Grid Grid.Column="1" Margin="5,0,0,0">
            <Grid.RowDefinitions>
              <RowDefinition Height="*"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 报警详情 -->
            <Border Grid.Row="0" Background="White" CornerRadius="5" Margin="0,0,0,5"
                    BorderBrush="#BDC3C7" BorderThickness="1">
              <Grid Margin="10">
                <Grid.RowDefinitions>
                  <RowDefinition Height="Auto"/>
                  <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="报警详情" FontSize="14" FontWeight="Bold" 
                           Foreground="#2C3E50" Margin="0,0,0,10"/>

                <ScrollViewer Grid.Row="1">
                  <StackPanel Spacing="10" IsVisible="{Binding SelectedAlarm, Converter={x:Static ObjectConverters.IsNotNull}}">
                    <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                      <StackPanel>
                        <TextBlock Text="基本信息" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding SelectedAlarm.RuleName, StringFormat='规则: {0}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.DeviceName, StringFormat='设备: {0}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.Level, StringFormat='级别: {0}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.Status, StringFormat='状态: {0}'}" FontSize="12"/>
                      </StackPanel>
                    </Border>

                    <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                      <StackPanel>
                        <TextBlock Text="数值信息" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding SelectedAlarm.CurrentValue, StringFormat='当前值: {0:F2}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.Threshold, StringFormat='阈值: {0:F2}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.Count, StringFormat='触发次数: {0}'}" FontSize="12"/>
                      </StackPanel>
                    </Border>

                    <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                      <StackPanel>
                        <TextBlock Text="时间信息" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Text="{Binding SelectedAlarm.TriggeredTime, StringFormat='触发时间: {0:yyyy-MM-dd HH:mm:ss}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.Duration, StringFormat='持续时间: {0}'}" FontSize="12"/>
                        <TextBlock Text="{Binding SelectedAlarm.AcknowledgedTime, StringFormat='确认时间: {0:yyyy-MM-dd HH:mm:ss}'}" 
                                   FontSize="12" IsVisible="{Binding SelectedAlarm.AcknowledgedTime, Converter={x:Static ObjectConverters.IsNotNull}}"/>
                      </StackPanel>
                    </Border>
                  </StackPanel>
                </ScrollViewer>
              </Grid>
            </Border>

            <!-- 报警操作 -->
            <Border Grid.Row="1" Background="White" CornerRadius="5" Margin="0,5,0,0"
                    BorderBrush="#BDC3C7" BorderThickness="1">
              <Grid Margin="10">
                <Grid.RowDefinitions>
                  <RowDefinition Height="Auto"/>
                  <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="报警操作" FontSize="14" FontWeight="Bold" 
                           Foreground="#2C3E50" Margin="0,0,0,10"/>

                <StackPanel Grid.Row="1" Spacing="10">
                  <!-- 确认报警 -->
                  <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                    <StackPanel>
                      <TextBlock Text="确认报警" FontWeight="Bold" Margin="0,0,0,5"/>
                      <TextBox Watermark="确认备注..." Text="{Binding AcknowledgmentComment}" 
                               AcceptsReturn="True" Height="60" Margin="0,0,0,5"/>
                      <Button Content="确认报警" Command="{Binding AcknowledgeAlarmCommand}"
                              Background="#27AE60" Foreground="White" HorizontalAlignment="Stretch"/>
                    </StackPanel>
                  </Border>

                  <!-- 其他操作 -->
                  <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                    <StackPanel Spacing="5">
                      <TextBlock Text="其他操作" FontWeight="Bold" Margin="0,0,0,5"/>
                      <Button Content="抑制报警" Command="{Binding SuppressAlarmCommand}"
                              Background="#E67E22" Foreground="White" HorizontalAlignment="Stretch"/>
                    </StackPanel>
                  </Border>
                </StackPanel>
              </Grid>
            </Border>
          </Grid>
        </Grid>
      </TabItem>

      <!-- 报警规则标签页 -->
      <TabItem Header="报警规则">
        <Grid>
          <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
          </Grid.ColumnDefinitions>

          <!-- 规则列表 -->
          <Border Grid.Column="0" Background="White" CornerRadius="5" Margin="0,0,5,0"
                  BorderBrush="#BDC3C7" BorderThickness="1">
            <Grid Margin="10">
              <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
              </Grid.RowDefinitions>

              <TextBlock Grid.Row="0" Text="报警规则列表" FontSize="16" FontWeight="Bold" 
                         Foreground="#2C3E50" Margin="0,0,0,10"/>

              <DataGrid Grid.Row="1" ItemsSource="{Binding AlarmRules}" 
                        SelectedItem="{Binding SelectedRule}"
                        AutoGenerateColumns="False" IsReadOnly="True"
                        GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                <DataGrid.Columns>
                  <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="50"/>
                  <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150"/>
                  <DataGridTextColumn Header="设备" Binding="{Binding DeviceName}" Width="120"/>
                  <DataGridTextColumn Header="级别" Binding="{Binding Level}" Width="60"/>
                  <DataGridTextColumn Header="条件" Binding="{Binding Condition}" Width="80"/>
                  <DataGridTextColumn Header="阈值" Binding="{Binding Threshold, StringFormat=F2}" Width="80"/>
                  <DataGridTextColumn Header="延迟" Binding="{Binding DelayTime}" Width="80"/>
                  <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*"/>
                </DataGrid.Columns>
              </DataGrid>
            </Grid>
          </Border>

          <!-- 规则操作 -->
          <Border Grid.Column="1" Background="White" CornerRadius="5" Margin="5,0,0,0"
                  BorderBrush="#BDC3C7" BorderThickness="1">
            <Grid Margin="10">
              <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
              </Grid.RowDefinitions>

              <TextBlock Grid.Row="0" Text="规则操作" FontSize="14" FontWeight="Bold" 
                         Foreground="#2C3E50" Margin="0,0,0,10"/>

              <StackPanel Grid.Row="1" Spacing="10">
                <Button Content="添加规则" Command="{Binding AddRuleCommand}"
                        Background="#27AE60" Foreground="White" HorizontalAlignment="Stretch"/>
                <Button Content="编辑规则" Command="{Binding EditRuleCommand}"
                        Background="#3498DB" Foreground="White" HorizontalAlignment="Stretch"/>
                <Button Content="删除规则" Command="{Binding DeleteRuleCommand}"
                        Background="#E74C3C" Foreground="White" HorizontalAlignment="Stretch"/>
              </StackPanel>
            </Grid>
          </Border>
        </Grid>
      </TabItem>

      <!-- 统计信息标签页 -->
      <TabItem Header="统计信息">
        <Border Background="White" CornerRadius="5" BorderBrush="#BDC3C7" BorderThickness="1">
          <Grid Margin="20">
            <Grid.RowDefinitions>
              <RowDefinition Height="Auto"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="报警统计信息" FontSize="16" FontWeight="Bold" 
                       Foreground="#2C3E50" Margin="0,0,0,20"/>

            <Grid Grid.Row="1">
              <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
              </Grid.ColumnDefinitions>

              <!-- 总体统计 -->
              <StackPanel Grid.Column="0" Spacing="15">
                <TextBlock Text="总体统计" FontSize="14" FontWeight="Bold"/>
                
                <Border Background="#F8F9FA" CornerRadius="5" Padding="15">
                  <Grid>
                    <Grid.ColumnDefinitions>
                      <ColumnDefinition Width="*"/>
                      <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="总报警数:" FontWeight="Bold"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Statistics.TotalAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="活动报警:" FontWeight="Bold"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Statistics.ActiveAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="已确认:" FontWeight="Bold"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Statistics.AcknowledgedAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="已恢复:" FontWeight="Bold"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Statistics.RecoveredAlarms}" HorizontalAlignment="Right"/>
                  </Grid>
                </Border>

                <!-- 按级别统计 -->
                <Border Background="#F8F9FA" CornerRadius="5" Padding="15">
                  <Grid>
                    <Grid.ColumnDefinitions>
                      <ColumnDefinition Width="*"/>
                      <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                      <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="按级别统计" FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="严重:" Foreground="#E74C3C"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Statistics.CriticalAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="高级:" Foreground="#E67E22"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding Statistics.HighAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="中级:" Foreground="#F39C12"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding Statistics.MediumAlarms}" HorizontalAlignment="Right"/>
                    
                    <TextBlock Grid.Row="4" Grid.Column="0" Text="低级:" Foreground="#27AE60"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding Statistics.LowAlarms}" HorizontalAlignment="Right"/>
                  </Grid>
                </Border>
              </StackPanel>

              <!-- 按设备统计 -->
              <StackPanel Grid.Column="1" Spacing="15" Margin="20,0,0,0">
                <TextBlock Text="按设备统计" FontSize="14" FontWeight="Bold"/>
                
                <Border Background="#F8F9FA" CornerRadius="5" Padding="15">
                  <ScrollViewer MaxHeight="300">
                    <ItemsControl ItemsSource="{Binding Statistics.AlarmsByDevice}">
                      <ItemsControl.ItemTemplate>
                        <DataTemplate>
                          <Grid Margin="0,2">
                            <Grid.ColumnDefinitions>
                              <ColumnDefinition Width="*"/>
                              <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="{Binding Key}"/>
                            <TextBlock Grid.Column="1" Text="{Binding Value}" FontWeight="Bold"/>
                          </Grid>
                        </DataTemplate>
                      </ItemsControl.ItemTemplate>
                    </ItemsControl>
                  </ScrollViewer>
                </Border>
              </StackPanel>
            </Grid>
          </Grid>
        </Border>
      </TabItem>
    </TabControl>

    <!-- 状态栏 -->
    <Border Grid.Row="2" Background="#ECF0F1" Padding="10,5">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="20">
          <StackPanel Orientation="Horizontal">
            <TextBlock Text="活动报警:" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding Statistics.ActiveAlarms}" FontWeight="Bold" Foreground="#E74C3C" VerticalAlignment="Center"/>
          </StackPanel>
          <StackPanel Orientation="Horizontal">
            <TextBlock Text="今日报警:" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding Statistics.TotalAlarms}" FontWeight="Bold" VerticalAlignment="Center"/>
          </StackPanel>
        </StackPanel>
        
        <TextBlock Grid.Column="1" Text="{Binding Statistics.StatisticsTime, StringFormat='更新时间: {0:HH:mm:ss}'}" 
                   VerticalAlignment="Center" FontSize="12" Foreground="#7F8C8D"/>
      </Grid>
    </Border>
  </Grid>
</UserControl>
