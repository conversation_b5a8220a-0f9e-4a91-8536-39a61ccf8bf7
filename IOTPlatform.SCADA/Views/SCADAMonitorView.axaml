<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:IOTPlatform.SCADA.ViewModels"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="IOTPlatform.SCADA.Views.SCADAMonitorView"
             x:DataType="vm:SCADAMonitorViewModel">

  <Design.DataContext>
    <vm:SCADAMonitorViewModel/>
  </Design.DataContext>

  <Grid>
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
    </Grid.RowDefinitions>

    <!-- 顶部工具栏 -->
    <Border Grid.Row="0" Background="#2C3E50" Padding="10">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
          <TextBlock Text="SCADA监控系统" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,20,0"/>
          <Border Background="Green" CornerRadius="10" Width="20" Height="20" Margin="0,0,10,0"/>
          <TextBlock Text="{Binding ConnectionStatus}" FontSize="14" Foreground="White" VerticalAlignment="Center"/>
      <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
        <Button Content="连接" Command="{Binding ConnectCommand}" 
                IsEnabled="{Binding !IsConnected}"
                Background="#27AE60" Foreground="White" Padding="15,5"/>
        <Button Content="断开" Command="{Binding DisconnectCommand}" 
                IsEnabled="{Binding IsConnected}"
                Background="#E74C3C" Foreground="White" Padding="15,5"/>
        <Button Content="刷新" Command="{Binding RefreshDataCommand}"
                Background="#3498DB" Foreground="White" Padding="15,5"/>
        <Button Content="报警管理" Background="#E67E22" Foreground="White" Padding="15,5" 
                Click="OnAlarmManagementClick"/>
        <Button Content="历史趋势" Background="#9B59B6" Foreground="White" Padding="15,5" 
                Click="OnTrendChartClick"/>
      </StackPanel>                  Background="#3498DB" Foreground="White" Padding="15,5"/>
        </StackPanel>
      </Grid>
    </Border>

    <!-- 主要内容区域 -->
    <Grid Grid.Row="1" Margin="10">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="2*"/>
        <ColumnDefinition Width="*"/>
      </Grid.ColumnDefinitions>

      <!-- 工艺参数显示区域 -->
      <Border Grid.Column="0" Background="White" CornerRadius="5" Margin="0,0,5,0" 
              BorderBrush="#BDC3C7" BorderThickness="1">
        <Grid Margin="10">
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
          </Grid.RowDefinitions>

          <TextBlock Grid.Row="0" Text="工艺参数监控" FontSize="16" FontWeight="Bold" 
                     Foreground="#2C3E50" Margin="0,0,0,10"/>

          <!-- 参数显示网格 -->
          <Grid Grid.Row="1">
            <Grid.RowDefinitions>
              <RowDefinition Height="*"/>
              <RowDefinition Height="*"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
              <ColumnDefinition Width="*"/>
              <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 温度显示 -->
            <Border Grid.Row="0" Grid.Column="0" Background="#F8F9FA" CornerRadius="5" Margin="5" Padding="10">
              <StackPanel>
                <TextBlock Text="温度" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                <TextBlock Text="{Binding Temperature}" FontSize="20" 
                           FontWeight="Bold" Foreground="#E74C3C" HorizontalAlignment="Center"/>
                <TextBlock Text="°C" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
              </StackPanel>
            </Border>

            <!-- 压力显示 -->
            <Border Grid.Row="0" Grid.Column="1" Background="#F8F9FA" CornerRadius="5" Margin="5" Padding="10">
              <StackPanel>
                <TextBlock Text="压力" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                <TextBlock Text="{Binding Pressure}" FontSize="20" 
                           FontWeight="Bold" Foreground="#3498DB" HorizontalAlignment="Center"/>
                <TextBlock Text="MPa" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
              </StackPanel>
            </Border>

            <!-- 流量显示 -->
            <Border Grid.Row="1" Grid.Column="0" Background="#F8F9FA" CornerRadius="5" Margin="5" Padding="10">
              <StackPanel>
                <TextBlock Text="流量" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                <TextBlock Text="{Binding FlowRate}" FontSize="20" 
                           FontWeight="Bold" Foreground="#27AE60" HorizontalAlignment="Center"/>
                <TextBlock Text="L/min" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
              </StackPanel>
            </Border>

            <!-- 液位显示 -->
            <Border Grid.Row="1" Grid.Column="1" Background="#F8F9FA" CornerRadius="5" Margin="5" Padding="10">
              <StackPanel>
                <TextBlock Text="液位" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                <TextBlock Text="{Binding Level}" FontSize="20" 
                           FontWeight="Bold" Foreground="#9B59B6" HorizontalAlignment="Center"/>
                <TextBlock Text="%" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
              </StackPanel>
            </Border>

            <!-- 设备控制 -->
            <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#F8F9FA" CornerRadius="5" Margin="5" Padding="10">
              <StackPanel>
                <TextBlock Text="设备控制" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                
                <!-- 泵控制 -->
                <Grid Margin="0,0,0,10">
                  <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                  </Grid.ColumnDefinitions>
                  
                  <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="5">
                    <TextBlock Text="水泵1:" VerticalAlignment="Center"/>
                    <Button Content="启动" Command="{Binding StartPump1Command}" 
                            IsEnabled="{Binding !Pump1Running}" Background="#27AE60" Foreground="White" Padding="10,5"/>
                    <Button Content="停止" Command="{Binding StopPump1Command}" 
                            IsEnabled="{Binding Pump1Running}" Background="#E74C3C" Foreground="White" Padding="10,5"/>
                  </StackPanel>
                  
                  <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="5">
                    <TextBlock Text="水泵2:" VerticalAlignment="Center"/>
                    <Button Content="启动" Command="{Binding StartPump2Command}" 
                            IsEnabled="{Binding !Pump2Running}" Background="#27AE60" Foreground="White" Padding="10,5"/>
                    <Button Content="停止" Command="{Binding StopPump2Command}" 
                            IsEnabled="{Binding Pump2Running}" Background="#E74C3C" Foreground="White" Padding="10,5"/>
                  </StackPanel>
                </Grid>
                
                <!-- 阀门控制 -->
                <Grid>
                  <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                  </Grid.ColumnDefinitions>
                  
                  <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="5">
                    <TextBlock Text="阀门1:" VerticalAlignment="Center"/>
                    <Button Content="开启" Command="{Binding OpenValve1Command}" 
                            IsEnabled="{Binding !Valve1Open}" Background="#27AE60" Foreground="White" Padding="10,5"/>
                    <Button Content="关闭" Command="{Binding CloseValve1Command}" 
                            IsEnabled="{Binding Valve1Open}" Background="#E74C3C" Foreground="White" Padding="10,5"/>
                  </StackPanel>
                  
                  <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="5">
                    <TextBlock Text="阀门2:" VerticalAlignment="Center"/>
                    <Button Content="开启" Command="{Binding OpenValve2Command}" 
                            IsEnabled="{Binding !Valve2Open}" Background="#27AE60" Foreground="White" Padding="10,5"/>
                    <Button Content="关闭" Command="{Binding CloseValve2Command}" 
                            IsEnabled="{Binding Valve2Open}" Background="#E74C3C" Foreground="White" Padding="10,5"/>
                  </StackPanel>
                </Grid>
              </StackPanel>
            </Border>
          </Grid>
        </Grid>
      </Border>

      <!-- 右侧信息面板 -->
      <Grid Grid.Column="1" Margin="5,0,0,0">
        <Grid.RowDefinitions>
          <RowDefinition Height="*"/>
          <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 设备状态 -->
        <Border Grid.Row="0" Background="White" CornerRadius="5" Margin="0,0,0,5"
                BorderBrush="#BDC3C7" BorderThickness="1">
          <Grid Margin="10">
            <Grid.RowDefinitions>
              <RowDefinition Height="Auto"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="设备状态" FontSize="14" FontWeight="Bold" 
                       Foreground="#2C3E50" Margin="0,0,0,10"/>

            <ScrollViewer Grid.Row="1">
              <ItemsControl ItemsSource="{Binding Devices}">
                <ItemsControl.ItemTemplate>
                  <DataTemplate>
                    <Border Background="#F8F9FA" CornerRadius="3" Margin="0,2" Padding="8">
                      <Grid>
                        <Grid.ColumnDefinitions>
                          <ColumnDefinition Width="*"/>
                          <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                          <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                          <TextBlock Text="{Binding Value}" FontSize="10" Foreground="#7F8C8D"/>
                          <TextBlock Text="{Binding Status}" FontSize="10" Foreground="#7F8C8D"/>
                        </StackPanel>
                        
                        <Border Grid.Column="1" Background="Green" CornerRadius="10" Width="15" Height="15"/>
                      </Grid>
                    </Border>
                  </DataTemplate>
                </ItemsControl.ItemTemplate>
              </ItemsControl>
            </ScrollViewer>
          </Grid>
        </Border>

        <!-- 系统信息 -->
        <Border Grid.Row="1" Background="White" CornerRadius="5" Margin="0,5,0,0"
                BorderBrush="#BDC3C7" BorderThickness="1">
          <Grid Margin="10">
            <Grid.RowDefinitions>
              <RowDefinition Height="Auto"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="系统信息" FontSize="14" FontWeight="Bold" 
                       Foreground="#2C3E50" Margin="0,0,0,10"/>

            <StackPanel Grid.Row="1" Spacing="10">
              <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                <StackPanel>
                  <TextBlock Text="连接状态" FontSize="12" FontWeight="Bold"/>
                  <TextBlock Text="{Binding ConnectionStatus}" FontSize="14" Foreground="#27AE60"/>
                </StackPanel>
              </Border>
              
              <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                <StackPanel>
                  <TextBlock Text="设备统计" FontSize="12" FontWeight="Bold"/>
                  <TextBlock Text="在线设备: 6" FontSize="10" Foreground="#27AE60"/>
                  <TextBlock Text="离线设备: 0" FontSize="10" Foreground="#E74C3C"/>
                </StackPanel>
              </Border>
              
              <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                <StackPanel>
                  <TextBlock Text="运行状态" FontSize="12" FontWeight="Bold"/>
                  <TextBlock Text="系统正常" FontSize="10" Foreground="#27AE60"/>
                  <TextBlock Text="2024-12-27 15:30:00" FontSize="10" Foreground="#7F8C8D"/>
                </StackPanel>
              </Border>
            </StackPanel>
          </Grid>
        </Border>
      </Grid>
    </Grid>
  </Grid>
</UserControl>
