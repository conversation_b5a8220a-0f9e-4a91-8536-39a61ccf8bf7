<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:IOTPlatform.SCADA.ViewModels"
             xmlns:iot="using:IOTControls"
             mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
             x:Class="IOTPlatform.SCADA.Views.TrendChartView"
             x:DataType="vm:TrendChartViewModel">

  <Design.DataContext>
    <vm:TrendChartViewModel/>
  </Design.DataContext>

  <Grid>
    <Grid.RowDefinitions>
      <RowDefinition Height="Auto"/>
      <RowDefinition Height="*"/>
      <RowDefinition Height="Auto"/>
    </Grid.RowDefinitions>

    <!-- 工具栏 -->
    <Border Grid.Row="0" Background="#2C3E50" Padding="10">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧标题和时间选择 -->
        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
          <TextBlock Text="历史数据趋势图" FontSize="18" FontWeight="Bold" Foreground="White" Margin="0,0,20,0"/>
          
          <TextBlock Text="开始时间:" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
          <DatePicker SelectedDate="{Binding SelectedStartTime}" Margin="0,0,10,0"/>
          <TimePicker SelectedTime="{Binding SelectedStartTime}" Margin="0,0,20,0"/>
          
          <TextBlock Text="结束时间:" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
          <DatePicker SelectedDate="{Binding SelectedEndTime}" Margin="0,0,10,0"/>
          <TimePicker SelectedTime="{Binding SelectedEndTime}" Margin="0,0,20,0"/>
        </StackPanel>

        <!-- 右侧操作按钮 -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
          <Button Content="加载数据" Command="{Binding LoadDataCommand}" 
                  Background="#27AE60" Foreground="White" Padding="15,5"/>
          <Button Content="刷新" Command="{Binding RefreshCommand}" 
                  Background="#3498DB" Foreground="White" Padding="15,5"/>
          <Button Content="放大" Command="{Binding ZoomInCommand}" 
                  Background="#9B59B6" Foreground="White" Padding="15,5"/>
          <Button Content="缩小" Command="{Binding ZoomOutCommand}" 
                  Background="#9B59B6" Foreground="White" Padding="15,5"/>
          <Button Content="重置" Command="{Binding ResetZoomCommand}" 
                  Background="#E67E22" Foreground="White" Padding="15,5"/>
          <Button Content="导出" Command="{Binding ExportDataCommand}" 
                  Background="#34495E" Foreground="White" Padding="15,5"/>
        </StackPanel>
      </Grid>
    </Border>

    <!-- 主要内容区域 -->
    <Grid Grid.Row="1" Margin="10">
      <Grid.ColumnDefinitions>
        <ColumnDefinition Width="*"/>
        <ColumnDefinition Width="300"/>
      </Grid.ColumnDefinitions>

      <!-- 趋势图区域 -->
      <Border Grid.Column="0" Background="White" CornerRadius="5" Margin="0,0,10,0"
              BorderBrush="#BDC3C7" BorderThickness="1">
        <Grid Margin="10">
          <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
          </Grid.RowDefinitions>

          <!-- 图表标题 -->
          <TextBlock Grid.Row="0" Text="{Binding Config.Title}" FontSize="16" FontWeight="Bold" 
                     HorizontalAlignment="Center" Margin="0,0,0,10"/>

          <!-- IOTCurve 趋势图 -->
          <Border Grid.Row="1" BorderBrush="#E0E0E0" BorderThickness="1">
            <iot:IOTCurve x:Name="TrendChart"
                          Background="White"
                          ShowGrid="True"
                          GridColor="LightGray"
                          XAxisTitle="{Binding Config.XAxisTitle}"
                          YAxisTitle="{Binding Config.YAxisTitle}"
                          AutoScale="{Binding Config.AutoScale}"
                          MinY="{Binding Config.MinY}"
                          MaxY="{Binding Config.MaxY}"/>
          </Border>

          <!-- 加载指示器 -->
          <Border Grid.Row="1" Background="#80000000" IsVisible="{Binding IsLoading}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
              <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
              <TextBlock Text="正在加载数据..." Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
          </Border>
        </Grid>
      </Border>

      <!-- 右侧控制面板 -->
      <Grid Grid.Column="1">
        <Grid.RowDefinitions>
          <RowDefinition Height="*"/>
          <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 数据系列管理 -->
        <Border Grid.Row="0" Background="White" CornerRadius="5" Margin="0,0,0,5"
                BorderBrush="#BDC3C7" BorderThickness="1">
          <Grid Margin="10">
            <Grid.RowDefinitions>
              <RowDefinition Height="Auto"/>
              <RowDefinition Height="*"/>
              <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="数据系列" FontSize="14" FontWeight="Bold" 
                       Foreground="#2C3E50" Margin="0,0,0,10"/>

            <ScrollViewer Grid.Row="1">
              <ItemsControl ItemsSource="{Binding DataSeries}">
                <ItemsControl.ItemTemplate>
                  <DataTemplate>
                    <Border Background="#F8F9FA" CornerRadius="3" Margin="0,2" Padding="8">
                      <Grid>
                        <Grid.ColumnDefinitions>
                          <ColumnDefinition Width="Auto"/>
                          <ColumnDefinition Width="*"/>
                          <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 颜色指示器 -->
                        <Rectangle Grid.Column="0" Width="4" Height="20" Fill="{Binding LineColor}" 
                                   VerticalAlignment="Center" Margin="0,0,8,0"/>

                        <!-- 系列信息 -->
                        <StackPanel Grid.Column="1">
                          <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                          <TextBlock Text="{Binding DevicePath}" FontSize="10" Foreground="#7F8C8D"/>
                          <StackPanel Orientation="Horizontal">
                            <TextBlock Text="数据点: " FontSize="10" Foreground="#7F8C8D"/>
                            <TextBlock Text="{Binding DataPointCount}" FontSize="10" Foreground="#7F8C8D"/>
                          </StackPanel>
                        </StackPanel>

                        <!-- 操作按钮 -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="5">
                          <CheckBox IsChecked="{Binding IsVisible}" ToolTip.Tip="显示/隐藏"/>
                          <Button Content="×" FontSize="12" Width="20" Height="20" 
                                  Command="{Binding $parent[UserControl].DataContext.RemoveSeriesCommand}"
                                  CommandParameter="{Binding}"
                                  Background="#E74C3C" Foreground="White"/>
                        </StackPanel>
                      </Grid>
                    </Border>
                  </DataTemplate>
                </ItemsControl.ItemTemplate>
              </ItemsControl>
            </ScrollViewer>

            <Button Grid.Row="2" Content="添加系列" Margin="0,10,0,0"
                    Background="#27AE60" Foreground="White" HorizontalAlignment="Stretch"/>
          </Grid>
        </Border>

        <!-- 统计信息 -->
        <Border Grid.Row="1" Background="White" CornerRadius="5" Margin="0,5,0,0"
                BorderBrush="#BDC3C7" BorderThickness="1">
          <Grid Margin="10">
            <Grid.RowDefinitions>
              <RowDefinition Height="Auto"/>
              <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="统计信息" FontSize="14" FontWeight="Bold" 
                       Foreground="#2C3E50" Margin="0,0,0,10"/>

            <ScrollViewer Grid.Row="1">
              <StackPanel Spacing="10">
                <!-- 总体统计 -->
                <Border Background="#F8F9FA" CornerRadius="3" Padding="10">
                  <StackPanel>
                    <TextBlock Text="总体统计" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal">
                      <TextBlock Text="数据系列: " FontSize="10"/>
                      <TextBlock Text="{Binding Statistics.TotalSeries}" FontSize="10" FontWeight="Bold"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                      <TextBlock Text="数据点: " FontSize="10"/>
                      <TextBlock Text="{Binding Statistics.TotalDataPoints}" FontSize="10" FontWeight="Bold"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                      <TextBlock Text="时间跨度: " FontSize="10"/>
                      <TextBlock Text="{Binding Statistics.DataSpan}" FontSize="10" FontWeight="Bold"/>
                    </StackPanel>
                  </StackPanel>
                </Border>

                <!-- 系列详细统计 -->
                <ItemsControl ItemsSource="{Binding Statistics.SeriesStats}">
                  <ItemsControl.ItemTemplate>
                    <DataTemplate>
                      <Border Background="#F8F9FA" CornerRadius="3" Padding="8" Margin="0,2">
                        <StackPanel>
                          <TextBlock Text="{Binding Value.SeriesName}" FontSize="11" FontWeight="Bold"/>
                          <StackPanel Orientation="Horizontal">
                            <TextBlock Text="最小值: " FontSize="9"/>
                            <TextBlock Text="{Binding Value.MinValue, StringFormat=F2}" FontSize="9"/>
                          </StackPanel>
                          <StackPanel Orientation="Horizontal">
                            <TextBlock Text="最大值: " FontSize="9"/>
                            <TextBlock Text="{Binding Value.MaxValue, StringFormat=F2}" FontSize="9"/>
                          </StackPanel>
                          <StackPanel Orientation="Horizontal">
                            <TextBlock Text="平均值: " FontSize="9"/>
                            <TextBlock Text="{Binding Value.AverageValue, StringFormat=F2}" FontSize="9"/>
                          </StackPanel>
                        </StackPanel>
                      </Border>
                    </DataTemplate>
                  </ItemsControl.ItemTemplate>
                </ItemsControl>
              </StackPanel>
            </ScrollViewer>
          </Grid>
        </Border>
      </Grid>
    </Grid>

    <!-- 状态栏 -->
    <Border Grid.Row="2" Background="#ECF0F1" Padding="10,5">
      <Grid>
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
        
        <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
          <TextBlock Text="自动刷新间隔:" VerticalAlignment="Center"/>
          <ComboBox SelectedItem="{Binding Config.RefreshInterval}" Width="120">
            <ComboBox.Items>
              <x:TimeSpan>00:00:30</x:TimeSpan>
              <x:TimeSpan>00:01:00</x:TimeSpan>
              <x:TimeSpan>00:05:00</x:TimeSpan>
              <x:TimeSpan>00:10:00</x:TimeSpan>
            </ComboBox.Items>
          </ComboBox>
        </StackPanel>
      </Grid>
    </Border>
  </Grid>
</UserControl>
