using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using IOTPlatform.SCADA.ViewModels;
using IOTControls;
using System.Collections.Specialized;
using Avalonia.Threading;

namespace IOTPlatform.SCADA.Views;

public partial class TrendChartView : UserControl
{
    private TrendChartViewModel? _viewModel;
    private IOTCurve? _trendChart;

    public TrendChartView()
    {
        InitializeComponent();
        InitializeChart();
    }

    private void InitializeComponent()
    {
        AvaloniaXamlLoader.Load(this);
        _trendChart = this.FindControl<IOTCurve>("TrendChart");
    }

    private void InitializeChart()
    {
        if (_trendChart != null)
        {
            // 设置图表样式
            _trendChart.Background = Avalonia.Media.Brushes.White;
            _trendChart.BorderBrush = Avalonia.Media.Brushes.Gray;
            _trendChart.BorderThickness = new Avalonia.Thickness(1);
        }
    }

    protected override void OnDataContextChanged(EventArgs e)
    {
        base.OnDataContextChanged(e);

        // 取消之前的订阅
        if (_viewModel != null)
        {
            _viewModel.CurveItems.CollectionChanged -= OnCurveItemsChanged;
        }

        // 订阅新的ViewModel
        _viewModel = DataContext as TrendChartViewModel;
        if (_viewModel != null)
        {
            _viewModel.CurveItems.CollectionChanged += OnCurveItemsChanged;
            UpdateChartData();
        }
    }

    private void OnCurveItemsChanged(object? sender, NotifyCollectionChangedEventArgs e)
    {
        Dispatcher.UIThread.Post(UpdateChartData, DispatcherPriority.Render);
    }

    private void UpdateChartData()
    {
        if (_trendChart == null || _viewModel == null) return;

        try
        {
            // 刷新图表
            _trendChart.InvalidateVisual();
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常，避免影响UI
            System.Diagnostics.Debug.WriteLine($"更新图表数据时发生错误: {ex.Message}");
        }
    }
}
