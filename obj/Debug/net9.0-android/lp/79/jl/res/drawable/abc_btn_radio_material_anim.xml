<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<animated-selector
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        tools:ignore="NewApi">
    <item
            android:id="@+id/on"
            android:state_checked="true"
            android:drawable="@drawable/btn_radio_on_mtrl" />
    <item
            android:id="@+id/off"
            android:drawable="@drawable/btn_radio_off_mtrl" />
    <transition
            android:fromId="@+id/on"
            android:toId="@+id/off"
            android:drawable="@drawable/btn_radio_on_to_off_mtrl_animation" />
    <transition
            android:fromId="@+id/off"
            android:toId="@+id/on"
            android:drawable="@drawable/btn_radio_off_to_on_mtrl_animation" />
</animated-selector>
