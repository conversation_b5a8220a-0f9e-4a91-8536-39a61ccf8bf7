<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk7/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk7.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk7/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk7.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk8/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk8.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk8/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk8.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.common/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.common/1.9.10.2/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlinx.coroutines.core.jvm/1.7.3.2/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Core.Jvm.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlinx.coroutines.core.jvm/1.7.3.2/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Core.Jvm.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlinx.coroutines.android/1.7.3.2/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Android.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlinx.coroutines.android/1.7.3.2/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Android.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.google.guava.listenablefuture/1.0.0.16/buildTransitive/net6.0-android31.0/Xamarin.Google.Guava.ListenableFuture.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.google.guava.listenablefuture/1.0.0.16/buildTransitive/net6.0-android31.0/Xamarin.Google.Guava.ListenableFuture.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation.jvm/1.9.1.4/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Annotation.Jvm.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation.jvm/1.9.1.4/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Annotation.Jvm.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.collection.jvm/1.3.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.Jvm.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.collection.jvm/1.3.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.Jvm.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation/1.9.1.4/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Annotation.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation/1.9.1.4/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Annotation.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.collection/1.3.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.collection/1.3.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.versionedparcelable/1.1.1.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VersionedParcelable.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.versionedparcelable/1.1.1.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VersionedParcelable.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.tracing.tracing/1.1.0.8/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Tracing.Tracing.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.tracing.tracing/1.1.0.8/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Tracing.Tracing.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.startup.startupruntime/1.1.1.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Startup.StartupRuntime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.startup.startupruntime/1.1.1.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Startup.StartupRuntime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.concurrent.futures/1.1.0.16/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Concurrent.Futures.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.concurrent.futures/1.1.0.16/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Concurrent.Futures.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.profileinstaller.profileinstaller/1.3.1.4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.profileinstaller.profileinstaller/1.3.1.4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.common/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.common/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.arch.core.common/2.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.arch.core.common/2.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.arch.core.runtime/2.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.arch.core.runtime/2.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.interpolator/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Interpolator.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.interpolator/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Interpolator.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation.experimental/1.3.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Experimental.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation.experimental/1.3.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Experimental.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.core/1.12.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.core/1.12.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.customview/1.1.0.20/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.customview/1.1.0.20/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.viewpager/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.viewpager/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.vectordrawable/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.vectordrawable/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.vectordrawable.animated/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.Animated.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.vectordrawable.animated/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.Animated.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.savedstate/1.2.1.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.savedstate/1.2.1.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.resourceinspection.annotation/1.0.1.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ResourceInspection.Annotation.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.resourceinspection.annotation/1.0.1.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ResourceInspection.Annotation.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.loader/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Loader.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.loader/1.1.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Loader.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.core.core.ktx/1.12.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.Core.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.core.core.ktx/1.12.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.Core.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodelsavedstate/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodelsavedstate/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.process/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Process.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.process/2.6.2.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Process.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.activity/1.8.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.activity/1.8.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.fragment/1.6.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.fragment/1.6.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.emoji2/1.4.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.emoji2/1.4.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.emoji2.viewshelper/1.4.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.ViewsHelper.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.emoji2.viewshelper/1.4.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.ViewsHelper.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.drawerlayout/1.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DrawerLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.drawerlayout/1.2.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DrawerLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.cursoradapter/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CursorAdapter.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.cursoradapter/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CursorAdapter.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.core.splashscreen/1.0.1.16/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Core.SplashScreen.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.core.splashscreen/1.0.1.16/buildTransitive/net8.0-android34.0/Xamarin.AndroidX.Core.SplashScreen.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.appcompat.appcompatresources/1.6.1.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.AppCompatResources.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.appcompat.appcompatresources/1.6.1.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.AppCompatResources.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.appcompat/1.6.1.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.appcompat/1.6.1.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.json/10.0.0-preview.5.25277.114/buildTransitive/net8.0/System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json/10.0.0-preview.5.25277.114/buildTransitive/net8.0/System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.webassembly/2.88.9/buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.webassembly/2.88.9/buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets')" />
    <Import Project="$(NuGetPackageRoot)skiasharp.nativeassets.android/2.88.9/buildTransitive/net6.0-android30.0/SkiaSharp.NativeAssets.Android.targets" Condition="Exists('$(NuGetPackageRoot)skiasharp.nativeassets.android/2.88.9/buildTransitive/net6.0-android30.0/SkiaSharp.NativeAssets.Android.targets')" />
    <Import Project="$(NuGetPackageRoot)prism.events/9.0.537/buildTransitive/Prism.Events.targets" Condition="Exists('$(NuGetPackageRoot)prism.events/9.0.537/buildTransitive/Prism.Events.targets')" />
    <Import Project="$(NuGetPackageRoot)prism.container.abstractions/9.0.107/buildTransitive/Prism.Container.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)prism.container.abstractions/9.0.107/buildTransitive/Prism.Container.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)prism.core/9.0.537/buildTransitive/Prism.Core.targets" Condition="Exists('$(NuGetPackageRoot)prism.core/9.0.537/buildTransitive/Prism.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)prism.container.dryioc/9.0.107/buildTransitive/Prism.Container.DryIoc.targets" Condition="Exists('$(NuGetPackageRoot)prism.container.dryioc/9.0.107/buildTransitive/Prism.Container.DryIoc.targets')" />
    <Import Project="$(NuGetPackageRoot)avalonia.buildservices/0.0.31/buildTransitive/Avalonia.BuildServices.targets" Condition="Exists('$(NuGetPackageRoot)avalonia.buildservices/0.0.31/buildTransitive/Avalonia.BuildServices.targets')" />
    <Import Project="$(NuGetPackageRoot)avalonia/11.3.2/buildTransitive/Avalonia.targets" Condition="Exists('$(NuGetPackageRoot)avalonia/11.3.2/buildTransitive/Avalonia.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder/10.0.0-preview.5.25277.114/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder/10.0.0-preview.5.25277.114/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)harfbuzzsharp.nativeassets.webassembly/8.3.1.1/buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets" Condition="Exists('$(NuGetPackageRoot)harfbuzzsharp.nativeassets.webassembly/8.3.1.1/buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets')" />
    <Import Project="$(NuGetPackageRoot)harfbuzzsharp.nativeassets.android/8.3.1.1/buildTransitive/net8.0-android34.0/HarfBuzzSharp.NativeAssets.Android.targets" Condition="Exists('$(NuGetPackageRoot)harfbuzzsharp.nativeassets.android/8.3.1.1/buildTransitive/net8.0-android34.0/HarfBuzzSharp.NativeAssets.Android.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm/8.4.0/buildTransitive/CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm/8.4.0/buildTransitive/CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
</Project>