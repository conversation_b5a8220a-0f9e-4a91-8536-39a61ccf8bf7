"restore":{"projectUniqueName":"/Users/<USER>/Study/IOTPlatform/IOTPlatform/IOTPlatform.Android/IOTPlatform.Android.csproj","projectName":"IOTPlatform.Android","projectPath":"/Users/<USER>/Study/IOTPlatform/IOTPlatform/IOTPlatform.Android/IOTPlatform.Android.csproj","outputPath":"/Users/<USER>/Study/IOTPlatform/IOTPlatform/IOTPlatform.Android/obj/","projectStyle":"PackageReference","centralPackageVersionsManagementEnabled":true,"originalTargetFrameworks":["net9.0-android"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0-android35.0":{"targetAlias":"net9.0-android","projectReferences":{"/Users/<USER>/Study/IOTPlatform/IOTPlatform/IOTPlatform/IOTPlatform.csproj":{"projectPath":"/Users/<USER>/Study/IOTPlatform/IOTPlatform/IOTPlatform/IOTPlatform.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0-android35.0":{"targetAlias":"net9.0-android","dependencies":{"Avalonia.Android":{"target":"Package","version":"[11.3.2, )","versionCentrallyManaged":true},"Microsoft.NET.ILLink.Tasks":{"suppressParent":"All","target":"Package","version":"[9.0.2, )","autoReferenced":true},"Prism.DryIoc.Avalonia":{"target":"Package","version":"[9.0.537.11300-pre, )","versionCentrallyManaged":true},"Serilog.Sinks.File":{"target":"Package","version":"[7.0.0, )","versionCentrallyManaged":true},"Xamarin.AndroidX.Core.SplashScreen":{"target":"Package","version":"[1.0.1.16, )","versionCentrallyManaged":true}},"centralPackageVersions":{"Avalonia":"11.3.2","Avalonia.Android":"11.3.2","Avalonia.Browser":"11.3.2","Avalonia.Desktop":"11.3.2","Avalonia.Diagnostics":"11.3.2","Avalonia.Fonts.Inter":"11.3.2","Avalonia.iOS":"11.3.2","Avalonia.Themes.Fluent":"11.3.2","Avalonia.Xaml.Behaviors":"11.3.0.6","CommunityToolkit.Mvvm":"8.4.0","FluentAvaloniaUI":"2.4.0-preview1","FluentValidation":"12.0.0","InfluxDB.Client":"4.19.0-dev.15190","Microsoft.Extensions.Configuration":"10.0.0-preview.5.25277.114","Microsoft.Extensions.Configuration.Binder":"10.0.0-preview.5.25277.114","Microsoft.Extensions.Configuration.FileExtensions":"10.0.0-preview.5.25277.114","Microsoft.Extensions.Configuration.Json":"10.0.0-preview.5.25277.114","MySql.Data":"9.3.0","Newtonsoft.Json":"13.0.3","Prism.Avalonia":"9.0.537.11300-pre","Prism.Core":"9.0.537","Prism.DryIoc.Avalonia":"9.0.537.11300-pre","Projektanker.Icons.Avalonia":"9.6.2","Projektanker.Icons.Avalonia.FontAwesome":"9.6.2","Projektanker.Icons.Avalonia.MaterialDesign":"9.6.2","Serilog":"4.3.1-dev-02373","Serilog.Sinks.Console":"6.0.1-dev-00953","Serilog.Sinks.File":"7.0.0","SqlSugarCore":"5.1.4.197-preview14","System.Collections":"4.3.0","System.ObjectModel":"4.3.0","Xamarin.AndroidX.Core.SplashScreen":"1.0.1.16"},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.Android":{"privateAssets":"all"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.200/PortableRuntimeIdentifierGraph.json"}}"runtimes":{"android-arm64":{"#import":[]},"android-x64":{"#import":[]}}